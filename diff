diff --git a/app/code/Webkul/MpApi/Api/SellerManagementInterface.php b/app/code/Webkul/MpApi/Api/SellerManagementInterface.php
index c04673d7d..2e3ab97bb 100644
--- a/app/code/Webkul/MpApi/Api/SellerManagementInterface.php
+++ b/app/code/Webkul/MpApi/Api/SellerManagementInterface.php
@@ -250,4 +250,12 @@ interface SellerManagementInterface
      */
     public function uploadImage($sellerId);
 
+    /**
+     * Upload multiple images (1-4 images, max 20MB total)
+     *
+     * @param int $sellerId
+     * @return string \Magento\Framework\Controller\Result\Json
+     */
+    public function uploadMultipleImages($sellerId);
+
 }
diff --git a/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php b/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php
index be5c8c616..275fa9997 100644
--- a/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php
+++ b/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php
@@ -4009,4 +4009,285 @@ class SellerManagement implements \Webkul\MpApi\Api\SellerManagementInterface
         }
     }
 
-}
+    /**
+     * Upload Multiple Images (1-4 images, max 20MB total)
+     *
+     * @param int $sellerId
+     * @return string
+     */
+    public function uploadMultipleImages($sellerId)
+    {
+        try {
+            if (!$this->isSeller($sellerId)) {
+                throw new LocalizedException(__('Invalid Seller'));
+            }
+
+            $images = $this->validateUploadRequest();
+            $this->validateImageFiles($images);
+            $uploadedImages = $this->processImageUploads($images);
+
+            $this->returnArrayData["images"] = $uploadedImages;
+            $this->returnArrayData["message"] = __('%1 image(s) have been successfully uploaded', count($uploadedImages));
+            $this->returnArrayData["success"] = true;
+            $this->returnArrayData["count"] = count($uploadedImages);
+
+            return $this->getJsonResponse($this->returnArrayData);
+
+        } catch (\Exception $e) {
+            $this->mpHelper->logDataInLogger($e->getMessage());
+            $this->returnArrayData["message"] = $e->getMessage();
+            $this->returnArrayData["success"] = false;
+            return $this->getJsonResponse($this->returnArrayData);
+        }
+    }
+
+
+    /**
+     * Validate upload request and extract images
+     *
+     * @return array
+     * @throws LocalizedException
+     */
+    private function validateUploadRequest(): array
+    {
+        $files = (array) $this->_request->getFiles();
+        if ($this->_request->getMethod() !== "POST" || empty($files)) {
+            throw new LocalizedException(__("Invalid Request."));
+        }
+
+        if (!isset($files['images']) || !is_array($files['images'])) {
+            throw new LocalizedException(__("No images found in request."));
+        }
+
+        $images = $files['images'];
+        $imageCount = count($images);
+
+        if ($imageCount < 1 || $imageCount > 4) {
+            throw new LocalizedException(__('Please upload between 1 and 4 images.'));
+        }
+
+        return $images;
+    }
+
+    /**
+     * Validate all image files
+     *
+     * @param array $images
+     * @throws LocalizedException
+     */
+    private function validateImageFiles(array $images): void
+    {
+        $totalSize = 0;
+        $maxTotalSize = 20 * 1024 * 1024;
+
+        foreach ($images as $index => $image) {
+            $this->validateSingleImage($image, $index);
+            $totalSize += $image['size'];
+        }
+
+        if ($totalSize > $maxTotalSize) {
+            throw new LocalizedException(__('Total file size exceeds maximum limit of 20MB.'));
+        }
+    }
+
+    /**
+     * Validate a single image file
+     *
+     * @param array $image
+     * @param int $index
+     * @throws LocalizedException
+     */
+    private function validateSingleImage(array $image, int $index): void
+    {
+        $allowedExtensions = ['jpeg', 'jpg', 'png', 'gif', 'webp', 'svg', 'avif', 'jfif'];
+        $maxIndividualSize = 5 * 1024 * 1024;
+
+        if (!isset($image['error']) || $image['error'] !== UPLOAD_ERR_OK) {
+            throw new LocalizedException(__('Upload error for image %1.', $index + 1));
+        }
+
+        if (!isset($image['size']) || $image['size'] > $maxIndividualSize) {
+            throw new LocalizedException(__('Image %1 exceeds maximum size of 5MB.', $index + 1));
+        }
+
+        if (!isset($image['name'])) {
+            throw new LocalizedException(__('Missing filename for image %1.', $index + 1));
+        }
+
+        $fileName = $image['name'];
+        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
+
+        if (!in_array($fileExtension, $allowedExtensions)) {
+            throw new LocalizedException(
+                __('Invalid file type for image %1. Allowed types: %2', $index + 1, implode(', ', $allowedExtensions))
+            );
+        }
+
+        if (!isset($image['tmp_name']) || !$this->isValidImageFile($image['tmp_name'])) {
+            throw new LocalizedException(__('File %1 is not a valid image.', $index + 1));
+        }
+    }
+
+    /**
+     * Process image uploads
+     *
+     * @param array $images
+     * @return array
+     * @throws LocalizedException
+     */
+    private function processImageUploads(array $images): array
+    {
+        $uploadedImages = [];
+        $target = $this->_mediaDirectory->getAbsolutePath(
+            $this->mediaConfig->getBaseTmpMediaPath()
+        );
+
+        foreach ($images as $index => $image) {
+            try {
+                $uploadedImage = $this->uploadSingleImage($image, $index, $target);
+                $uploadedImages[] = $uploadedImage;
+            } catch (\Exception $e) {
+                throw new LocalizedException(__('Error uploading image %1: %2', $index + 1, $e->getMessage()));
+            }
+        }
+
+        return $uploadedImages;
+    }
+
+    /**
+     * Upload a single image file
+     *
+     * @param array $image
+     * @param int $index
+     * @param string $target
+     * @return array
+     * @throws LocalizedException
+     */
+    private function uploadSingleImage(array $image, int $index, string $target): array
+    {
+        $tempPath = null;
+
+        try {
+            $tempPath = $this->createTemporaryFile($image);
+            $result = $this->executeFileUpload($image, $tempPath, $target);
+            return $this->formatUploadResult($result, $image, $index);
+
+        } finally {
+            $this->cleanupTemporaryResources($tempPath);
+        }
+    }
+
+    /**
+     * Create temporary file for image processing
+     *
+     * @param array $image
+     * @return string
+     * @throws LocalizedException
+     */
+    private function createTemporaryFile(array $image): string
+    {
+        $uniqueId = uniqid('img_', true);
+        $tempFileName = $uniqueId . '_' . $image['name'];
+        $tempPath = sys_get_temp_dir() . '/' . $tempFileName;
+
+        if (!move_uploaded_file($image['tmp_name'], $tempPath)) {
+            throw new LocalizedException(__('Failed to process image'));
+        }
+
+        return $tempPath;
+    }
+
+    /**
+     * Execute file upload using Magento's uploader
+     *
+     * @param array $image
+     * @param string $tempPath
+     * @param string $target
+     * @return array
+     */
+    private function executeFileUpload(array $image, string $tempPath, string $target): array
+    {
+        $_FILES['temp_image'] = [
+            'name' => $image['name'],
+            'type' => $image['type'],
+            'tmp_name' => $tempPath,
+            'error' => UPLOAD_ERR_OK,
+            'size' => $image['size']
+        ];
+
+        $fileUploader = $this->_fileUploaderFactory->create(['fileId' => 'temp_image']);
+        $fileUploader->setAllowedExtensions($this->getAllowedExtensions());
+        $fileUploader->setFilesDispersion(true);
+        $fileUploader->setAllowRenameFiles(true);
+
+        return $fileUploader->save($target);
+    }
+
+    /**
+     * Get allowed file extensions for upload
+     *
+     * @return array
+     */
+    private function getAllowedExtensions(): array
+    {
+        return [
+            'jpeg', 'jpg', 'png', 'gif', 'JPEG', 'JPG', 'PNG', 'GIF',
+            'webp', 'WEBP', 'svg', 'SVG', 'avif', 'AVIF', 'jfif', 'JFIF'
+        ];
+    }
+
+    /**
+     * Format upload result for response
+     *
+     * @param array $result
+     * @param array $image
+     * @param int $index
+     * @return array
+     */
+    private function formatUploadResult(array $result, array $image, int $index): array
+    {
+        unset($result['tmp_name'], $result['path']);
+
+        $result['url'] = $this->mediaConfig->getTmpMediaUrl($result['file']);
+        $result['file'] = $result['file'] . '.tmp';
+        $result['original_name'] = $image['name'];
+        $result['index'] = $index;
+
+        return $result;
+    }
+
+    /**
+     * Clean up temporary resources
+     *
+     * @param string|null $tempPath
+     */
+    private function cleanupTemporaryResources(?string $tempPath): void
+    {
+        if ($tempPath && file_exists($tempPath)) {
+            unlink($tempPath);
+        }
+
+        if (isset($_FILES['temp_image'])) {
+            unset($_FILES['temp_image']);
+        }
+    }
+
+
+
+    /**
+     * Check if uploaded file is a valid image
+     *
+     * @param string $tmpName
+     * @return bool
+     */
+    private function isValidImageFile(string $tmpName): bool
+    {
+        if (!file_exists($tmpName)) {
+            return false;
+        }
+
+        $imageInfo = getimagesize($tmpName);
+        return $imageInfo !== false;
+    }
+
+}
\ No newline at end of file
diff --git a/app/code/Webkul/MpApi/etc/webapi.xml b/app/code/Webkul/MpApi/etc/webapi.xml
index 6520df1a2..07dbcfe18 100644
--- a/app/code/Webkul/MpApi/etc/webapi.xml
+++ b/app/code/Webkul/MpApi/etc/webapi.xml
@@ -314,4 +314,14 @@
             <parameter name="sellerId" force="true">%customer_id%</parameter>
         </data>
     </route>
+
+    <route url="/V1/mpapi/uploadmultipleimages" method="POST">
+        <service class="Webkul\MpApi\Api\SellerManagementInterface" method="uploadMultipleImages"/>
+        <resources>
+            <resource ref="self"/>
+        </resources>
+        <data>
+            <parameter name="sellerId" force="true">%customer_id%</parameter>
+        </data>
+    </route>
 </routes>
