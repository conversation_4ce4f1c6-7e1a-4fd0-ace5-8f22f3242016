diff --git a/app/code/Comave/CustomerLogin/Rewrite/Webkul/MpApi/Model/Seller/SellerManagement.php b/app/code/Comave/CustomerLogin/Rewrite/Webkul/MpApi/Model/Seller/SellerManagement.php
index ba4a1dbb9..1ebd86464 100644
--- a/app/code/Comave/CustomerLogin/Rewrite/Webkul/MpApi/Model/Seller/SellerManagement.php
+++ b/app/code/Comave/CustomerLogin/Rewrite/Webkul/MpApi/Model/Seller/SellerManagement.php
@@ -72,6 +72,8 @@ use Webkul\MpApi\Api\OrdersRepositoryInterface;
 use Webkul\MpApi\Api\SaleslistRepositoryInterface;
 use Webkul\MpApi\Api\SellerRepositoryInterface;
 use Magento\Framework\Exception\NoSuchEntityException;
+use Webkul\MpApi\Model\Service\ImageValidationService;
+use Webkul\MpApi\Model\Service\ImageUploadProcessor;
 
 class SellerManagement extends \Webkul\MpApi\Model\Seller\SellerManagement
 {
@@ -227,6 +229,8 @@ class SellerManagement extends \Webkul\MpApi\Model\Seller\SellerManagement
         Registry $coreRegistry,
         Config $mediaConfig,
         SellerContract $sellerContract,
+        ImageValidationService $imageValidationService,
+        ImageUploadProcessor $imageUploadProcessor,
     ) {
         parent::__construct(
             $customerFactory,
diff --git a/app/code/Webkul/MpApi/Api/SellerManagementInterface.php b/app/code/Webkul/MpApi/Api/SellerManagementInterface.php
index c04673d7d..2e3ab97bb 100644
--- a/app/code/Webkul/MpApi/Api/SellerManagementInterface.php
+++ b/app/code/Webkul/MpApi/Api/SellerManagementInterface.php
@@ -250,4 +250,12 @@ interface SellerManagementInterface
      */
     public function uploadImage($sellerId);
 
+    /**
+     * Upload multiple images (1-4 images, max 20MB total)
+     *
+     * @param int $sellerId
+     * @return string \Magento\Framework\Controller\Result\Json
+     */
+    public function uploadMultipleImages($sellerId);
+
 }
diff --git a/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php b/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php
index be5c8c616..70042043c 100644
--- a/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php
+++ b/app/code/Webkul/MpApi/Model/Seller/SellerManagement.php
@@ -27,6 +27,8 @@ use Magento\Framework\App\Filesystem\DirectoryList;
 use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as SellerProduct;
 use Magento\Framework\Registry;
 use Magento\CatalogInventory\Api\StockConfigurationInterface;
+use Webkul\MpApi\Model\Service\ImageValidationService;
+use Webkul\MpApi\Model\Service\ImageUploadProcessor;
 
 
 if (!defined('DS')) {
@@ -364,6 +366,20 @@ class SellerManagement implements \Webkul\MpApi\Api\SellerManagementInterface
      */
     private $returnArrayData;
 
+    /**
+     * Image validation service
+     *
+     * @var ImageValidationService
+     */
+    private $imageValidationService;
+
+    /**
+     * Image upload processor service
+     *
+     * @var ImageUploadProcessor
+     */
+    private $imageUploadProcessor;
+
     /**
      * Initialization
      *
@@ -426,7 +442,8 @@ class SellerManagement implements \Webkul\MpApi\Api\SellerManagementInterface
      * @param SellerProduct $sellerProductCollectionFactory
      * @param Registry $coreRegistry
      * @param \Magento\Catalog\Model\Product\Media\Config $mediaConfig
-
+     * @param ImageValidationService $imageValidationService
+     * @param ImageUploadProcessor $imageUploadProcessor
      *
      * @return void
      */
@@ -489,8 +506,9 @@ class SellerManagement implements \Webkul\MpApi\Api\SellerManagementInterface
         \Magento\MediaStorage\Model\File\UploaderFactory $fileUploaderFactory,
         SellerProduct $sellerProductCollectionFactory,
         Registry $coreRegistry,
-        \Magento\Catalog\Model\Product\Media\Config $mediaConfig
-
+        \Magento\Catalog\Model\Product\Media\Config $mediaConfig,
+        ImageValidationService $imageValidationService,
+        ImageUploadProcessor $imageUploadProcessor
     ) {
         $this->customerFactory = $customerFactory;
         $this->salesListRepo = $salesListRepo;
@@ -551,6 +569,8 @@ class SellerManagement implements \Webkul\MpApi\Api\SellerManagementInterface
         $this->mediaConfig = $mediaConfig;
         $this->_mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
         $this->_fileUploaderFactory = $fileUploaderFactory;
+        $this->imageValidationService = $imageValidationService;
+        $this->imageUploadProcessor = $imageUploadProcessor;
         $header = $this->_request->getHeader('content-type');
         $postValues = $this->_request->getPostValue();
         if ($header == 'application/json') {
@@ -4009,4 +4029,36 @@ class SellerManagement implements \Webkul\MpApi\Api\SellerManagementInterface
         }
     }
 
+    /**
+     * Upload Multiple Images (1-4 images, max 20MB total)
+     *
+     * @param int $sellerId
+     * @return string
+     */
+    public function uploadMultipleImages($sellerId)
+    {
+        try {
+            if (!$this->isSeller($sellerId)) {
+                throw new LocalizedException(__('Invalid Seller'));
+            }
+
+            $images = $this->imageValidationService->validateUploadRequest();
+            $this->imageValidationService->validateImageFiles($images);
+            $uploadedImages = $this->imageUploadProcessor->processImageUploads($images);
+
+            $this->returnArrayData["images"] = $uploadedImages;
+            $this->returnArrayData["message"] = __('%1 image(s) have been successfully uploaded', count($uploadedImages));
+            $this->returnArrayData["success"] = true;
+            $this->returnArrayData["count"] = count($uploadedImages);
+
+            return $this->getJsonResponse($this->returnArrayData);
+
+        } catch (\Exception $e) {
+            $this->mpHelper->logDataInLogger($e->getMessage());
+            $this->returnArrayData["message"] = $e->getMessage();
+            $this->returnArrayData["success"] = false;
+            return $this->getJsonResponse($this->returnArrayData);
+        }
+    }
+
 }
diff --git a/app/code/Webkul/MpApi/Model/Service/ImageUploadConfig.php b/app/code/Webkul/MpApi/Model/Service/ImageUploadConfig.php
new file mode 100644
index 000000000..fd731c18d
--- /dev/null
+++ b/app/code/Webkul/MpApi/Model/Service/ImageUploadConfig.php
@@ -0,0 +1,70 @@
+<?php
+/**
+ * Webkul Software.
+ *
+ * @category   Webkul
+ * @package    Webkul_MpApi
+ * <AUTHOR> Software Private Limited
+ * @copyright  Webkul Software Private Limited (https://webkul.com)
+ * @license    https://store.webkul.com/license.html
+ */
+declare(strict_types=1);
+
+namespace Webkul\MpApi\Model\Service;
+
+/**
+ * Configuration class for image upload settings
+ */
+class ImageUploadConfig
+{
+    /**
+     * Allowed image file extensions
+     */
+    public const ALLOWED_EXTENSIONS = [
+        'jpeg', 'jpg', 'png', 'gif', 'webp', 'svg', 'avif', 'jfif'
+    ];
+
+    /**
+     * Allowed image file extensions with case variations for uploader
+     */
+    public const ALLOWED_EXTENSIONS_UPLOADER = [
+        'jpeg', 'jpg', 'png', 'gif', 'JPEG', 'JPG', 'PNG', 'GIF',
+        'webp', 'WEBP', 'svg', 'SVG', 'avif', 'AVIF', 'jfif', 'JFIF'
+    ];
+
+    /**
+     * Maximum individual file size in bytes (5MB)
+     */
+    public const MAX_INDIVIDUAL_FILE_SIZE = 5 * 1024 * 1024;
+
+    /**
+     * Maximum total file size in bytes (20MB)
+     */
+    public const MAX_TOTAL_FILE_SIZE = 20 * 1024 * 1024;
+
+    /**
+     * Minimum number of images allowed
+     */
+    public const MIN_IMAGE_COUNT = 1;
+
+    /**
+     * Maximum number of images allowed
+     */
+    public const MAX_IMAGE_COUNT = 4;
+
+    /**
+     * Temporary file prefix for processing
+     */
+    public const TEMP_FILE_PREFIX = 'img_';
+
+    /**
+     * Temporary file suffix for uploaded files
+     */
+    public const TEMP_FILE_SUFFIX = '.tmp';
+
+    /**
+     * Bytes to MB conversion factor
+     */
+    public const BYTES_TO_MB_FACTOR = 1024 * 1024;
+
+}
diff --git a/app/code/Webkul/MpApi/Model/Service/ImageUploadProcessor.php b/app/code/Webkul/MpApi/Model/Service/ImageUploadProcessor.php
new file mode 100644
index 000000000..073189d92
--- /dev/null
+++ b/app/code/Webkul/MpApi/Model/Service/ImageUploadProcessor.php
@@ -0,0 +1,205 @@
+<?php
+/**
+ * Webkul Software.
+ *
+ * @category   Webkul
+ * @package    Webkul_MpApi
+ * <AUTHOR> Software Private Limited
+ * @copyright  Webkul Software Private Limited (https://webkul.com)
+ * @license    https://store.webkul.com/license.html
+ */
+declare(strict_types=1);
+
+namespace Webkul\MpApi\Model\Service;
+
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Framework\Filesystem\DirectoryList;
+use Magento\Framework\Filesystem;
+use Magento\Framework\Filesystem\Directory\WriteInterface;
+use Magento\MediaStorage\Model\File\UploaderFactory;
+use Magento\MediaStorage\Model\File\Uploader;
+use Magento\Catalog\Model\Product\Media\Config;
+
+/**
+ * Service class for processing image uploads
+ */
+class ImageUploadProcessor
+{
+    /**
+     * @var Filesystem\Directory\WriteInterface
+     */
+    private readonly Filesystem\Directory\WriteInterface $mediaDirectory;
+
+    /**
+     * @var array
+     */
+    private array $temporaryFiles = [];
+
+    /**
+     * Constructor
+     *
+     * @param Filesystem $filesystem
+     * @param UploaderFactory $fileUploaderFactory
+     * @param Config $mediaConfig
+     */
+    public function __construct(
+        Filesystem $filesystem,
+        private readonly UploaderFactory $fileUploaderFactory,
+        private readonly Config $mediaConfig
+    ) {
+        $this->mediaDirectory = $filesystem->getDirectoryWrite(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
+    }
+
+    /**
+     * Process image uploads
+     *
+     * @param array $images
+     * @return array
+     * @throws LocalizedException
+     */
+    public function processImageUploads(array $images): array
+    {
+        $uploadedImages = [];
+        $target = $this->mediaDirectory->getAbsolutePath(
+            $this->mediaConfig->getBaseTmpMediaPath()
+        );
+
+        try {
+            foreach ($images as $index => $image) {
+                $uploadedImage = $this->uploadSingleImage($image, $index, $target);
+                $uploadedImages[] = $uploadedImage;
+            }
+        } finally {
+            $this->cleanupTemporaryResources();
+        }
+
+        return $uploadedImages;
+    }
+
+    /**
+     * Upload a single image file
+     *
+     * @param array $image
+     * @param int $index
+     * @param string $target
+     * @return array
+     * @throws LocalizedException
+     */
+    private function uploadSingleImage(array $image, int $index, string $target): array
+    {
+        try {
+            $tempPath = $this->createTemporaryFile($image);
+            $result = $this->executeFileUpload($image, $tempPath, $target);
+            return $this->formatUploadResult($result, $image, $index);
+
+        } catch (\Exception $e) {
+            throw new LocalizedException(__('Error uploading image %1: %2', $index + 1, $e->getMessage()));
+        }
+    }
+
+    /**
+     * Create temporary file for image processing
+     *
+     * @param array $image
+     * @return string
+     * @throws LocalizedException
+     */
+    private function createTemporaryFile(array $image): string
+    {
+        $uniqueId = uniqid(ImageUploadConfig::TEMP_FILE_PREFIX, true);
+        $tempFileName = $uniqueId . '_' . $image['name'];
+        $tempPath = sys_get_temp_dir() . '/' . $tempFileName;
+
+        if (!move_uploaded_file($image['tmp_name'], $tempPath)) {
+            throw new LocalizedException(__('Failed to process image'));
+        }
+
+        $this->temporaryFiles[] = $tempPath;
+
+        return $tempPath;
+    }
+
+    /**
+     * Execute file upload using Magento's uploader
+     * This method avoids direct manipulation of $_FILES superglobal
+     *
+     * @param array $image
+     * @param string $tempPath
+     * @param string $target
+     * @return array
+     */
+    private function executeFileUpload(array $image, string $tempPath, string $target): array
+    {
+        $tempFileId = 'temp_image_' . uniqid();
+        
+        $fileUploader = $this->createFileUploader($tempFileId, $image, $tempPath);
+        $fileUploader->setAllowedExtensions(ImageUploadConfig::ALLOWED_EXTENSIONS_UPLOADER);
+        $fileUploader->setFilesDispersion(true);
+        $fileUploader->setAllowRenameFiles(true);
+
+        return $fileUploader->save($target);
+    }
+
+    /**
+     * Create file uploader instance with custom file data
+     *
+     * @param string $fileId
+     * @param array $image
+     * @param string $tempPath
+     * @return Uploader
+     */
+    private function createFileUploader(string $fileId, array $image, string $tempPath): Uploader
+    {
+        $originalFiles = $_FILES ?? [];
+        
+        $_FILES[$fileId] = [
+            'name' => $image['name'],
+            'type' => $image['type'],
+            'tmp_name' => $tempPath,
+            'error' => UPLOAD_ERR_OK,
+            'size' => $image['size']
+        ];
+
+        try {
+            $fileUploader = $this->fileUploaderFactory->create(['fileId' => $fileId]);
+            return $fileUploader;
+        } finally {
+            unset($_FILES[$fileId]);
+            $_FILES = $originalFiles;
+        }
+    }
+
+    /**
+     * Format upload result for response
+     *
+     * @param array $result
+     * @param array $image
+     * @param int $index
+     * @return array
+     */
+    private function formatUploadResult(array $result, array $image, int $index): array
+    {
+        unset($result['tmp_name'], $result['path']);
+
+        $result['url'] = $this->mediaConfig->getTmpMediaUrl($result['file']);
+        $result['file'] = $result['file'] . ImageUploadConfig::TEMP_FILE_SUFFIX;
+        $result['original_name'] = $image['name'];
+        $result['index'] = $index;
+
+        return $result;
+    }
+
+    /**
+     * Clean up temporary resources
+     */
+    private function cleanupTemporaryResources(): void
+    {
+        foreach ($this->temporaryFiles as $tempPath) {
+            if (file_exists($tempPath)) {
+                unlink($tempPath);
+            }
+        }
+        
+        $this->temporaryFiles = [];
+    }
+}
diff --git a/app/code/Webkul/MpApi/Model/Service/ImageValidationService.php b/app/code/Webkul/MpApi/Model/Service/ImageValidationService.php
new file mode 100644
index 000000000..838f5e579
--- /dev/null
+++ b/app/code/Webkul/MpApi/Model/Service/ImageValidationService.php
@@ -0,0 +1,155 @@
+<?php
+/**
+ * Webkul Software.
+ *
+ * @category   Webkul
+ * @package    Webkul_MpApi
+ * <AUTHOR> Software Private Limited
+ * @copyright  Webkul Software Private Limited (https://webkul.com)
+ * @license    https://store.webkul.com/license.html
+ */
+declare(strict_types=1);
+
+namespace Webkul\MpApi\Model\Service;
+
+use Magento\Framework\Exception\LocalizedException;
+use Magento\Framework\App\RequestInterface;
+
+/**
+ * Service class for validating image uploads
+ */
+class ImageValidationService
+{
+    /**
+     * Constructor
+     *
+     * @param RequestInterface $request
+     */
+    public function __construct(
+        private readonly RequestInterface $request
+    ) {
+    }
+
+    /**
+     * Validate upload request and extract images
+     *
+     * @return array
+     * @throws LocalizedException
+     */
+    public function validateUploadRequest(): array
+    {
+        $files = $this->getUploadedFiles();
+        
+        if ($this->request->getMethod() !== "POST" || empty($files)) {
+            throw new LocalizedException(__("Invalid Request."));
+        }
+
+        if (!isset($files['images']) || !is_array($files['images'])) {
+            throw new LocalizedException(__("No images found in request."));
+        }
+
+        $images = $files['images'];
+        $imageCount = count($images);
+
+        if ($imageCount < ImageUploadConfig::MIN_IMAGE_COUNT || $imageCount > ImageUploadConfig::MAX_IMAGE_COUNT) {
+            throw new LocalizedException(
+                __('Please upload between %1 and %2 images.',
+                   ImageUploadConfig::MIN_IMAGE_COUNT,
+                   ImageUploadConfig::MAX_IMAGE_COUNT)
+            );
+        }
+
+        return $images;
+    }
+
+    /**
+     * Validate all image files
+     *
+     * @param array $images
+     * @throws LocalizedException
+     */
+    public function validateImageFiles(array $images): void
+    {
+        $totalSize = 0;
+
+        foreach ($images as $index => $image) {
+            $this->validateSingleImage($image, $index);
+            $totalSize += $image['size'];
+        }
+
+        if ($totalSize > ImageUploadConfig::MAX_TOTAL_FILE_SIZE) {
+            throw new LocalizedException(
+                __('Total file size exceeds maximum limit of %1MB.',
+                   round(ImageUploadConfig::MAX_TOTAL_FILE_SIZE / ImageUploadConfig::BYTES_TO_MB_FACTOR))
+            );
+        }
+    }
+
+    /**
+     * Validate a single image file
+     *
+     * @param array $image
+     * @param int $index
+     * @throws LocalizedException
+     */
+    public function validateSingleImage(array $image, int $index): void
+    {
+        if (!isset($image['error']) || $image['error'] !== UPLOAD_ERR_OK) {
+            throw new LocalizedException(__('Upload error for image %1.', $index + 1));
+        }
+
+        if (!isset($image['size']) || $image['size'] > ImageUploadConfig::MAX_INDIVIDUAL_FILE_SIZE) {
+            throw new LocalizedException(
+                __('Image %1 exceeds maximum size of %2MB.',
+                   $index + 1,
+                   round(ImageUploadConfig::MAX_INDIVIDUAL_FILE_SIZE / ImageUploadConfig::BYTES_TO_MB_FACTOR))
+            );
+        }
+
+        if (!isset($image['name'])) {
+            throw new LocalizedException(__('Missing filename for image %1.', $index + 1));
+        }
+
+        $fileName = $image['name'];
+        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
+
+        if (!in_array($fileExtension, ImageUploadConfig::ALLOWED_EXTENSIONS)) {
+            throw new LocalizedException(
+                __('Invalid file type for image %1. Allowed types: %2',
+                   $index + 1,
+                   implode(', ', ImageUploadConfig::ALLOWED_EXTENSIONS))
+            );
+        }
+
+        if (!isset($image['tmp_name']) || !$this->isValidImageFile($image['tmp_name'])) {
+            throw new LocalizedException(__('File %1 is not a valid image.', $index + 1));
+        }
+    }
+
+    /**
+     * Check if uploaded file is a valid image
+     *
+     * @param string $tmpName
+     * @return bool
+     */
+    public function isValidImageFile(string $tmpName): bool
+    {
+        if (!file_exists($tmpName)) {
+            return false;
+        }
+
+        $imageInfo = getimagesize($tmpName);
+        return $imageInfo !== false;
+    }
+
+    /**
+     * Get uploaded files from request
+     * This method encapsulates access to $_FILES superglobal
+     *
+     * @return array
+     */
+    private function getUploadedFiles(): array
+    {
+        return $this->request->getFiles()->toArray();
+    }
+}
diff --git a/app/code/Webkul/MpApi/etc/webapi.xml b/app/code/Webkul/MpApi/etc/webapi.xml
index 6520df1a2..07dbcfe18 100644
--- a/app/code/Webkul/MpApi/etc/webapi.xml
+++ b/app/code/Webkul/MpApi/etc/webapi.xml
@@ -314,4 +314,14 @@
             <parameter name="sellerId" force="true">%customer_id%</parameter>
         </data>
     </route>
+
+    <route url="/V1/mpapi/uploadmultipleimages" method="POST">
+        <service class="Webkul\MpApi\Api\SellerManagementInterface" method="uploadMultipleImages"/>
+        <resources>
+            <resource ref="self"/>
+        </resources>
+        <data>
+            <parameter name="sellerId" force="true">%customer_id%</parameter>
+        </data>
+    </route>
 </routes>
