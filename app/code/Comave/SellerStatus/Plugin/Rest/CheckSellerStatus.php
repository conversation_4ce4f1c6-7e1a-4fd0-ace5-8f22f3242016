<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Plugin\Rest;

use Comave\SellerStatus\Api\ActionableInterface;
use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatus\Model\RoleValidator\Active;
use Magento\Authorization\Model\UserContextInterface;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\AuthorizationException;
use Magento\Framework\Webapi\Rest\Response;
use Webkul\Marketplace\Helper\Data;

class CheckSellerStatus
{
    // private array $forbiddenRoutes = [
    //     'mpapi',
    //     'seller'
    // ];

    private array $allowedRoutes = [
        'uploadimage',
        'uploadmultipleimages'
    ];

    /**
     * @param UserContextInterface $userContext
     * @param Response $response
     * @param Data $mpHelper
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param RequestInterface $request
     */
    public function __construct(
        private readonly UserContextInterface $userContext,
        private readonly Response $response,
        private readonly Data $mpHelper,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly RequestInterface $request,
    ) {
    }

    /**
     * @param \Magento\Webapi\Controller\Rest $restController
     * @param callable $proceed
     * @param RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterDispatch(
        \Magento\Webapi\Controller\Rest $restController,
        \Magento\Framework\App\ResponseInterface $response
    ): \Magento\Framework\App\ResponseInterface {
        if (!$this->userContext->getUserId()) {
            return $response;
        }

        $isSeller = $this->mpHelper->isSeller();

        if ($isSeller === false) {
            return $response;
        }

        $pathInfo = $this->request->getPathInfo();
        $found = false;

        foreach ($this->forbiddenRoutes as $route) {
            if (!str_contains($pathInfo, $route)) {
                continue;
            }

            $found = true;
        }

        if (!$found) {
            return $response;
        }

        // Check if this is an allowed upload route
        foreach ($this->allowedRoutes as $allowedRoute) {
            if (str_contains($pathInfo, $allowedRoute)) {
                return $response;
            }
        }

        $sellerCompany = $this->sellerCompanyProvider->get();
        $allRoles = $this->companyUserRoleManagement->getRolesForCompanyUser(
            (int) $this->userContext->getUserId(),
            (int) $sellerCompany->getId()
        );
        $userRole = current($allRoles);

        if (empty($userRole) || $userRole->getRoleName() !== Active::ROLE_NAME) {
            $response->setException(
                new AuthorizationException(__('You are not allowed to use this endpoint'))
            );

            $response->clearHeaders();
            $response->sendResponse();
            return $this->response;
        }

        return $response;
    }
}
