<?php

declare(strict_types=1);

namespace Comave\SellerStatus\Plugin;

use Comave\SellerStatus\Api\SelfAwareInterface;
use Magento\Company\Api\Data\RoleExtensionInterface;
use Magento\Company\Api\Data\RoleExtensionInterfaceFactory;
use Magento\Company\Api\Data\RoleInterface;
use Magento\Framework\ObjectManagerInterface;

class GetInstanceForRole
{
    /**
     * @param ObjectManagerInterface $objectManager
     * @param RoleExtensionInterfaceFactory $extensionFactory
     */
    public function __construct(
        private readonly ObjectManagerInterface $objectManager,
        private readonly RoleExtensionInterfaceFactory $extensionFactory
    ) {
    }

    /**
     * @param RoleInterface $role
     * @param RoleExtensionInterface|null $roleExtension
     * @return RoleExtensionInterface
     */
    public function afterGetExtensionAttributes(
        RoleInterface $role,
        ?RoleExtensionInterface $roleExtension = null
    ): RoleExtensionInterface {
        $roleExtension ??= $this->extensionFactory->create();

        if ($roleExtension->getStatusInstance() === null) {
            $className = str_replace(' ', '', $role->getRoleName());
            $classFqn = '\\Comave\\SellerStatus\\Model\\RoleValidator\\' . $className;

            if (class_exists($classFqn)) {
                $classInstance = $this->objectManager->get($classFqn);

                if ($classInstance instanceof SelfAwareInterface) {
                    $role->setExtensionAttributes($roleExtension);
                    $roleExtension->setStatusInstance($classInstance);
                }
            }
        }

        return $roleExtension;
    }
}
