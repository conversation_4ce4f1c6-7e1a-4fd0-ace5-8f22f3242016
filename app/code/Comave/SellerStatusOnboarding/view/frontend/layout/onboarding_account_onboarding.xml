<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" layout="1column">
    <head>
        <css src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" src_type="url"/>
    </head>
    <body>
        <referenceContainer name="content">
            <block name="seller.onboarding.steps" cacheable="false" template="Comave_SellerStatusOnboarding::onboarding.phtml">
                <arguments>
                    <argument xsi:type="object" name="onboardingViewModel">Comave\SellerStatusOnboarding\ViewModel\OnboardingData</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
