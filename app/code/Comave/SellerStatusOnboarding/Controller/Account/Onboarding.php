<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Controller\Account;

use Comave\SellerStatus\Model\Command\SellerCompanyProvider;
use Comave\SellerStatusOnboarding\Model\SellerOnboardingDataComposite;
use Magento\Company\Api\CompanyUserRoleManagementInterface;
use Magento\Customer\Controller\AccountInterface;
use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\View\Result\Page;
use Webkul\Marketplace\Model\ResourceModel\Seller;
use Webkul\Marketplace\Model\SellerFactory;

class Onboarding implements AccountInterface, HttpGetActionInterface
{
    /**
     * @param SellerCompanyProvider $sellerCompanyProvider
     * @param CompanyUserRoleManagementInterface $companyUserRoleManagement
     * @param SellerOnboardingDataComposite $onboardingDataComposite
     * @param CurrentCustomer $currentCustomer
     * @param SellerFactory $sellerFactory
     * @param Seller $resourceModel
     * @param ManagerInterface $messageManager
     * @param ResultFactory $resultFactory
     */
    public function __construct(
        private readonly SellerCompanyProvider $sellerCompanyProvider,
        private readonly CompanyUserRoleManagementInterface $companyUserRoleManagement,
        private readonly SellerOnboardingDataComposite $onboardingDataComposite,
        private readonly CurrentCustomer $currentCustomer,
        private readonly SellerFactory $sellerFactory,
        private readonly Seller $resourceModel,
        private readonly ManagerInterface $messageManager,
        private readonly ResultFactory $resultFactory
    ) {
    }

    /**
     * @return Page|Redirect
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(): Page|Redirect
    {
        if (!$this->currentCustomer->getCustomerId()) {
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setPath('marketplace/account/login');
        }

        $userRoles = $this->companyUserRoleManagement->getRolesForCompanyUser(
            (int) $this->currentCustomer->getCustomerId(),
            (int) $this->sellerCompanyProvider->get()->getId()
        );
        $currentRole = current($userRoles ?? []);

        if (!empty($currentRole) && $currentRole->getRoleName() !== \Comave\SellerStatus\Model\RoleValidator\Onboarding::ROLE_NAME) {
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setPath('marketplace/account/dashboard');
        }

        $seller = $this->sellerFactory->create();
        $this->resourceModel->load($seller, $this->currentCustomer->getCustomerId(), 'seller_id');

        if (!$seller->getId()) {
            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setPath('marketplace/account/login');
        }

        $sellerData = $this->onboardingDataComposite->getStepData($seller);
        $isComplete = true;

        foreach ($sellerData as $sectionData) {
            if ($sectionData['is_complete']) {
                continue;
            }

            $isComplete = false;
            break;
        }

        if ($isComplete) {
            $this->messageManager->getMessages(true);

            return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)
                ->setPath('onboarding/account/pendingValidation');
        }

        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $resultPage->getConfig()->getTitle()->set(
            __('Complete your onboarding process')
        );

        return $resultPage;
    }
}
