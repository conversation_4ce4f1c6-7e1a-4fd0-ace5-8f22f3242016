<?php

declare(strict_types=1);

namespace Comave\SellerStatusOnboarding\Plugin;

use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\UrlInterface;

class Onboarding
{
    /**
     * @param UrlInterface $urlBuilder
     * @param RequestInterface $request
     */
    public function __construct(
        private readonly UrlInterface $urlBuilder,
        private readonly RequestInterface $request
    ) {
    }

    /**
     * @param \Comave\SellerStatus\Model\RoleValidator\Onboarding $onboardingRoleInstance
     * @param $result,
     * @param ResponseInterface $response
     * @return void
     */
    public function afterAct(
        \Comave\SellerStatus\Model\RoleValidator\Onboarding $onboardingRoleInstance,
        $result,
        ResponseInterface $response
    ): void {
        if ($response->isRedirect()) {
            return;
        }

        $response->setRedirect(
            $this->urlBuilder->getUrl('onboarding/account/onboarding')
        );
        $this->request->setDispatched();
    }
}
