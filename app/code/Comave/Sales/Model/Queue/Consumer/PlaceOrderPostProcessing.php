<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Sales\Model\Queue\Consumer;

use Comave\Sales\Api\PostProcessingMessageInterface;
use Comave\Sales\Model\ProcessorTypeManager;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\State\InputMismatchException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

class PlaceOrderPostProcessing
{
    public const string SELLER_EMAIL_TOPIC_NAME = 'sales.place-order.post-processing.seller-email.topic';

    /**
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Comave\Sales\Model\ProcessorTypeManager $processorTypeManager
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly OrderRepositoryInterface $orderRepository,
        private readonly ProcessorTypeManager $processorTypeManager,
    ) {
    }

    /**
     * @param \Comave\Sales\Api\PostProcessingMessageInterface $message
     * @return void
     */
    public function execute(PostProcessingMessageInterface $message): void
    {
        $processingType = $message->getProcessingType();
        if (empty($processingType)) {
            $this->logger->warning(
                'Failed to retrieved the processing type.',
                [
                    'queueMessage' => $message->getData(),
                ]
            );
        } else {
            try {
                $order = $this->orderRepository->get($message->getOrderId());
                $processor = $this->processorTypeManager->get($processingType);
                if (!empty($processor)) {
                    $processor->process($order);
                } else {
                    $this->logger->warning(
                        'There is no post processor defined for this processing type.',
                        [
                            'queueMessage' => $message->getData(),
                        ]
                    );
                }
            } catch (InputException|NoSuchEntityException|InputMismatchException|LocalizedException $exception) {
                $this->logger->warning(
                    $exception->getMessage(),
                    [
                        'queueMessage' => $message->getData(),
                    ]
                );
            }
        }
    }
}
