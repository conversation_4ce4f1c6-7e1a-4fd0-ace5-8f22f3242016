<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/topology.xsd">
    <exchange name="sales.place-order.post-processing.exchange" connection="amqp" type="topic">
        <binding id="sales.place-order.post-processing.seller-email.exchange.binding"
                 topic="sales.place-order.post-processing.seller-email.topic"
                 destinationType="queue"
                 destination="sales.place-order.post-processing.seller-email"/>
    </exchange>
</config>
