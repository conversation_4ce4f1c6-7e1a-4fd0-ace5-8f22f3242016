<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\CustomerLogin\Rewrite\Webkul\MpApi\Model\Seller;

use Comave\MaskedEmail\Service\SellerContract;
use Exception;
use Magento\Backend\Model\Url;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Helper\Image;
use Magento\Catalog\Model\Product\Media\Config;
use Magento\Catalog\Model\ProductFactory;
use Magento\Catalog\Model\ProductRepository\MediaGalleryProcessor;
use Magento\Catalog\Model\ResourceModel\Product;
use Magento\CatalogInventory\Api\StockRegistryInterface as StockItem;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\Session;
use Magento\Directory\Model\CountryFactory;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\FilterFactory;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Magento\Framework\Api\Search\FilterGroupFactory;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\DB\Transaction;
use Magento\Framework\Event\Manager;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\Json\Helper\Data;
use Magento\Framework\Registry;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\UrlInterface;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Magento\Sales\Api\CreditmemoManagementInterface;
use Magento\Sales\Api\CreditmemoRepositoryInterface;
use Magento\Sales\Api\InvoiceManagementInterface;
use Magento\Sales\Api\InvoiceRepositoryInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\ShipmentRepositoryInterface;
use Magento\Sales\Model\Order\CreditmemoFactory;
use Magento\Sales\Model\Order\Email\Sender\CreditmemoSender;
use Magento\Sales\Model\Order\Email\Sender\InvoiceSender;
use Magento\Sales\Model\Order\Email\Sender\ShipmentSender;
use Magento\Sales\Model\Order\ShipmentFactory;
use Magento\Shipping\Model\CarrierFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\UrlRewrite\Model\UrlRewriteFactory;
use Webkul\Marketplace\Api\Data\FeedbackcountInterfaceFactory;
use Webkul\Marketplace\Api\Data\OrdersInterfaceFactory;
use Webkul\Marketplace\Api\Data\ProductInterfaceFactory;
use Webkul\Marketplace\Api\Data\SaleslistInterfaceFactory;
use Webkul\Marketplace\Api\Data\SellerInterfaceFactory;
use Webkul\Marketplace\Controller\Product\SaveProduct;
use Webkul\Marketplace\Helper\Email;
use Webkul\Marketplace\Helper\Orders;
use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory as SellerProduct;
use Webkul\MpApi\Api\Data\FeedbackInterfaceFactory;
use Webkul\MpApi\Api\Data\ResponseInterface;
use Webkul\MpApi\Api\FeedbackRepositoryInterface;
use Webkul\MpApi\Api\OrdersRepositoryInterface;
use Webkul\MpApi\Api\SaleslistRepositoryInterface;
use Webkul\MpApi\Api\SellerRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Webkul\MpApi\Model\Service\ImageValidationService;
use Webkul\MpApi\Model\Service\ImageUploadProcessor;

class SellerManagement extends \Webkul\MpApi\Model\Seller\SellerManagement
{
    /**
     * @var \Magento\Framework\Api\SearchCriteriaBuilder
     */
    private SearchCriteriaBuilder $searchCriteriaBuilder;
    /**
     * @var \Magento\Framework\Api\FilterBuilder
     */
    private FilterBuilder $filterBuilder;
    /**
     * @var \Magento\Framework\Api\Search\FilterGroupBuilder
     */
    private FilterGroupBuilder $filterGroupBuilder;
    /**
     * @var \Magento\CatalogInventory\Api\StockRegistryInterface
     */
    private StockItem $stockItem;
    /**
     * @var \Comave\MaskedEmail\Service\SellerContract
     */
    private SellerContract $sellerContract;

    /**
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Webkul\MpApi\Api\SaleslistRepositoryInterface $salesListRepo
     * @param \Webkul\MpApi\Api\SellerRepositoryInterface $sellerRepo
     * @param \Webkul\MpApi\Api\OrdersRepositoryInterface $ordersRepo
     * @param \Webkul\MpApi\Api\FeedbackRepositoryInterface $feedbackRepo
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Customer\Api\Data\CustomerInterface $customerInterface
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Directory\Model\CountryFactory $country
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Webkul\Marketplace\Api\Data\SellerInterfaceFactory $sellerFactory
     * @param \Webkul\Marketplace\Helper\Data $marketplaceHelper
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Magento\Sales\Api\InvoiceRepositoryInterface $invoiceRepository
     * @param \Magento\Sales\Model\Order\CreditmemoFactory $creditmemoFactory
     * @param \Magento\Sales\Model\Order\Email\Sender\InvoiceSender $invoiceSender
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $date
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param \Magento\Sales\Model\Order\ShipmentFactory $shipmentFactory
     * @param \Magento\Sales\Model\Order\Email\Sender\ShipmentSender $shipmentSender
     * @param \Magento\Sales\Model\Order\Email\Sender\CreditmemoSender $creditmemoSender
     * @param \Magento\Framework\Event\Manager $eventManager
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepoInterface
     * @param \Webkul\Marketplace\Api\Data\ProductInterfaceFactory $mpProductFactory
     * @param \Webkul\Marketplace\Api\Data\SaleslistInterfaceFactory $saleslistFactory
     * @param \Magento\Framework\Filesystem\DirectoryList $directoryList
     * @param \Webkul\Marketplace\Api\Data\OrdersInterfaceFactory $mpOrdersFactory
     * @param \Magento\Sales\Api\InvoiceManagementInterface $invoiceManagementInterface
     * @param \Magento\Framework\DB\Transaction $dbTransaction
     * @param \Webkul\Marketplace\Helper\Email $emailHelper
     * @param \Magento\Backend\Model\Url $backendUrl
     * @param \Magento\UrlRewrite\Model\UrlRewriteFactory $urlRewriteFactory
     * @param \Magento\Framework\UrlInterface $urlInterface
     * @param \Webkul\Marketplace\Helper\Orders $orderHelper
     * @param \Magento\Sales\Api\CreditmemoManagementInterface $creditmemoManagementInterface
     * @param \Magento\Sales\Api\CreditmemoRepositoryInterface $creditmemoRepositoryInterface
     * @param \Magento\Catalog\Helper\Image $imageHelper
     * @param \Webkul\MpApi\Api\Data\FeedbackInterfaceFactory $feedbackFactory
     * @param \Webkul\Marketplace\Api\Data\FeedbackcountInterfaceFactory $feedbackcountFactory
     * @param \Webkul\MpApi\Api\Data\ResponseInterface $responseInterface
     * @param \Magento\Framework\Filesystem\Driver\File $driverFile
     * @param \Magento\Framework\Filesystem\Io\File $file
     * @param \Webkul\Marketplace\Controller\Product\SaveProduct $saveProduct
     * @param \Magento\Sales\Api\ShipmentRepositoryInterface $shipmentRepository
     * @param \Magento\Shipping\Model\CarrierFactory $carrierFactory
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @param \Magento\Framework\Api\FilterFactory $filter
     * @param \Magento\Framework\Api\Search\FilterGroupFactory $filterGroup
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Magento\Framework\Api\FilterBuilder $filterBuilder
     * @param \Magento\Framework\Api\Search\FilterGroupBuilder $filterGroupBuilder
     * @param \Magento\Catalog\Model\ProductRepository\MediaGalleryProcessor $mediaProcessor
     * @param \Magento\Customer\Api\AccountManagementInterface $accountManagement
     * @param \Magento\Catalog\Model\ProductFactory $productFactory
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magento\Catalog\Model\ResourceModel\Product $productResourceModel
     * @param \Magento\CatalogInventory\Api\StockRegistryInterface $stockItem
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\MediaStorage\Model\File\UploaderFactory $fileUploaderFactory
     * @param \Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory $sellerProductCollectionFactory
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Catalog\Model\Product\Media\Config $mediaConfig
     * @param \Comave\MaskedEmail\Service\SellerContract $sellerContract
     */
    public function __construct(
        CustomerFactory $customerFactory,
        SaleslistRepositoryInterface $salesListRepo,
        SellerRepositoryInterface $sellerRepo,
        OrdersRepositoryInterface $ordersRepo,
        FeedbackRepositoryInterface $feedbackRepo,
        Session $customerSession,
        CustomerInterface $customerInterface,
        CustomerRepositoryInterface $customerRepository,
        CountryFactory $country,
        StoreManagerInterface $storeManager,
        SellerInterfaceFactory $sellerFactory,
        \Webkul\Marketplace\Helper\Data $marketplaceHelper,
        OrderRepositoryInterface $orderRepository,
        InvoiceRepositoryInterface $invoiceRepository,
        CreditmemoFactory $creditmemoFactory,
        InvoiceSender $invoiceSender,
        DateTime $date,
        ResourceConnection $resourceConnection,
        ShipmentFactory $shipmentFactory,
        ShipmentSender $shipmentSender,
        CreditmemoSender $creditmemoSender,
        Manager $eventManager,
        ProductRepositoryInterface $productRepoInterface,
        ProductInterfaceFactory $mpProductFactory,
        SaleslistInterfaceFactory $saleslistFactory,
        DirectoryList $directoryList,
        OrdersInterfaceFactory $mpOrdersFactory,
        InvoiceManagementInterface $invoiceManagementInterface,
        Transaction $dbTransaction,
        Email $emailHelper,
        Url $backendUrl,
        UrlRewriteFactory $urlRewriteFactory,
        UrlInterface $urlInterface,
        Orders $orderHelper,
        CreditmemoManagementInterface $creditmemoManagementInterface,
        CreditmemoRepositoryInterface $creditmemoRepositoryInterface,
        Image $imageHelper,
        FeedbackInterfaceFactory $feedbackFactory,
        FeedbackcountInterfaceFactory $feedbackcountFactory,
        ResponseInterface $responseInterface,
        Filesystem\Driver\File $driverFile,
        File $file,
        SaveProduct $saveProduct,
        ShipmentRepositoryInterface $shipmentRepository,
        CarrierFactory $carrierFactory,
        Data $jsonHelper,
        SearchCriteriaInterface $searchCriteria,
        FilterFactory $filter,
        FilterGroupFactory $filterGroup,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        MediaGalleryProcessor $mediaProcessor,
        AccountManagementInterface $accountManagement,
        ProductFactory $productFactory,
        RequestInterface $request,
        Product $productResourceModel,
        StockItem $stockItem,
        Filesystem $filesystem,
        UploaderFactory $fileUploaderFactory,
        SellerProduct $sellerProductCollectionFactory,
        Registry $coreRegistry,
        Config $mediaConfig,
        SellerContract $sellerContract,
        ImageValidationService $imageValidationService,
        ImageUploadProcessor $imageUploadProcessor,
    ) {
        parent::__construct(
            $customerFactory,
            $salesListRepo,
            $sellerRepo,
            $ordersRepo,
            $feedbackRepo,
            $customerSession,
            $customerInterface,
            $customerRepository,
            $country,
            $storeManager,
            $sellerFactory,
            $marketplaceHelper,
            $orderRepository,
            $invoiceRepository,
            $creditmemoFactory,
            $invoiceSender,
            $date,
            $resourceConnection,
            $shipmentFactory,
            $shipmentSender,
            $creditmemoSender,
            $eventManager,
            $productRepoInterface,
            $mpProductFactory,
            $saleslistFactory,
            $directoryList,
            $mpOrdersFactory,
            $invoiceManagementInterface,
            $dbTransaction,
            $emailHelper,
            $backendUrl,
            $urlRewriteFactory,
            $urlInterface,
            $orderHelper,
            $creditmemoManagementInterface,
            $creditmemoRepositoryInterface,
            $imageHelper,
            $feedbackFactory,
            $feedbackcountFactory,
            $responseInterface,
            $driverFile,
            $file,
            $saveProduct,
            $shipmentRepository,
            $carrierFactory,
            $jsonHelper,
            $searchCriteria,
            $filter,
            $filterGroup,
            $mediaProcessor,
            $accountManagement,
            $productFactory,
            $request,
            $productResourceModel,
            $filesystem,
            $fileUploaderFactory,
            $sellerProductCollectionFactory,
            $coreRegistry,
            $mediaConfig,
            $imageValidationService,
            $imageUploadProcessor
        );
        $this->sellerContract = $sellerContract;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
        $this->stockItem = $stockItem;
    }

    /**
     * Get seller products
     *
     * @param int $id
     * @return \Magento\Framework\Controller\Result\Json
     * @throws LocalizedException
     */
    public function getSellerProducts($id)
    {
        try {
            if (!$this->isSeller($id)) {
                throw new LocalizedException(
                    __('Invalid seller')
                );
            }
            $returnArray = [];

            $filters = array();
            $requestParams = $this->_request->getParams();

            $collection = $this->mpProductFactory->create()
                ->getCollection()
                ->addFieldToSelect('mageproduct_id')
                ->addFieldToFilter('seller_id', $id)
                ->addFieldToFilter('status', 1)
                ->setOrder('mageproduct_id');

            $prodIds = $collection->getData();

            $mageproductIds = array_column($prodIds, 'mageproduct_id');

            if (empty($requestParams)) {
                $filters[] = $this->filterBuilder->setField("entity_id")->setConditionType("in")->setValue(
                    implode(',', $mageproductIds)
                )->create();
            }

            foreach ($requestParams as $key => $value) {
                if ($key == "mageproduct_id") {
                    if (in_array($value, $mageproductIds)) {
                        $filters[] = $this->filterBuilder->setField("entity_id")->setConditionType("eq")->setValue(
                            $value
                        )->create();
                    }
                }
            }

            if (isset($requestParams['searchCriteria'])) {
                foreach ($requestParams['searchCriteria']['filter_groups'] as $searcfilterGroup) {
                    foreach ($searcfilterGroup['filters'] as $searchFilter) {
                        $filters[] = $this->filterBuilder
                            ->setField($searchFilter['field'])
                            ->setConditionType($searchFilter['condition_type'])
                            ->setValue($searchFilter['value'])
                            ->create();
                    }
                }
            }

            foreach ($filters as $filterData) {
                $filterGroup[] = $this->filterGroupBuilder->addFilter($filterData)->create();
            }

            $searchCriteria = $this->searchCriteriaBuilder->setFilterGroups($filterGroup)->create();
            $data = $this->productRepoInterface->getList($searchCriteria);


            if ($collection->getSize() == 0) {
                throw new LocalizedException(
                    __('no result found')
                );
            } else {
                $items = [];

                foreach ($data->getItems() as $coll) {
                    $qty = 0;
                    $childData = [];
                    $productId = $coll->getId();

                    $product = $this->productRepoInterface->getById($productId);

                    $productStock = $this->stockItem->getStockItem($productId);
                    if ($productStock) {
                        $qty = $productStock->getQty();
                    }

                    if ($product->getTypeId() == "configurable") {
                        $_children = $product->getTypeInstance()->getUsedProducts($product);
                        $dataOptions = $product->getTypeInstance()->getConfigurableOptions($product);

                        foreach ($_children as $child) {

                            $productStock = $this->stockItem->getStockItem($child->getEntityId());
                            if ($productStock) {
                                $qty = $productStock->getQty();
                            } else {
                                $qty = 0;
                            }

                            $options = array();
                            foreach ($dataOptions as $optionData) {
                                foreach ($optionData as $value) {
                                    $attrCode = $value['attribute_code'];
                                    if ($child[$attrCode] == $value['value_index']) {
                                        $options[$attrCode] = $value['option_title'];
                                    }
                                }
                            }

                            $childData[] = array_merge([
                                'parent_id' => $child->getParentId(),
                                'name' => $child->getName(),
                                'type' => $child->getTypeId(),
                                'sku' => $child->getSku(),
                                'price' => $child->getPrice(),
                                'qty' => $qty,
                                'status' => $child->getStatus(),
                                'weight' => $child->getWeight(),
                                'mageproduct_id' => $child->getEntityId(),
                                $options,
                            ]);
                        }
                    }

                    if ($product) {
                        if ($product->getTypeId() == "configurable") {
                            $items[] = array_merge([
                                'name' => $product->getName(),
                                'type' => $product->getTypeId(),
                                'sku' => $product->getSku(),
                                'price' => $product->getPrice(),
                                'qty' => $qty,
                                'status' => $product->getStatus(),
                                'specialPrice' => $product->getSpecialPrice(),
                                'weight' => $product->getWeight(),
                                'mageproduct_id' => $product->getId(),
                            ], $childData);
                        } else {
                            $items[] = array_merge([
                                'name' => $product->getName(),
                                'type' => $product->getTypeId(),
                                'sku' => $product->getSku(),
                                'price' => $product->getPrice(),
                                'qty' => $qty,
                                'status' => $product->getStatus(),
                                'specialPrice' => $product->getSpecialPrice(),
                                'weight' => $product->getWeight(),
                                'mageproduct_id' => $product->getId(),
                            ]);
                        }
                    }
                }

                $returnArray['status'] = self::SUCCESS;
                $returnArray['total_count'] = count($items);
                $returnArray['items'] = $items;

                return $this->getJsonResponse($returnArray);
            }
        } catch (LocalizedException $e) {
            $returnArray['error'] = $e->getMessage();
            $returnArray['status'] = self::LOCAL_ERROR;

            return $this->getJsonResponse($returnArray);
        } catch (Exception $e) {
            $this->mpHelper->logDataInLogger($e);
            $returnArray['error'] = __('Invalid Request');
            $returnArray['status'] = self::SEVERE_ERROR;

            return $this->getJsonResponse($returnArray);
        }
    }

    /**
     * Save Product
     *
     * @param int $id
     * @return \Magento\Framework\Controller\Result\Json
     */
    public function saveProduct($id)
    {
        try {
            $customer = $this->customerRepository->getById($id);
            $hasCommissions = false;
            if (!empty($customer->getCustomAttribute('wkv_comm_inc'))) {
                $hasCommissions = (bool)$customer->getCustomAttribute('wkv_comm_inc')->getValue();
            }

            $percent = 10;
            if ($customer->getCustomAttribute('wkv_comave_comm')) {
                $percent = $customer->getCustomAttribute('wkv_comave_comm')->getValue();
            }

            $productData = $this->_request->getPostValue();
            if (!$this->isSellerValid($productData, (int)$id)) {
                return $this->getJsonResponse([
                    'error' => 1,
                    'message' => 'You are not authorised to assing this product',
                ]);
            }

            if (isset($productData['product']['price'])) {
                $originalPrice = $productData['product']['price'];
                if ($hasCommissions) {
                    $comm = $productData['product']['price'] * ($percent / 100);
                    $productData['product']['price'] += $comm;
                }
            }

            if (isset($productData['product']['entity_id'])) {

                $productData['id'] = $productData['product']['entity_id'];
                $product = $this->productRepoInterface->getById($productData['product']['entity_id']);
                if ($product) {
                    $model = $this->mpProductFactory->create()->getCollection()
                        ->addFieldToFilter(
                            'mageproduct_id',
                            $product->getId()
                        )->addFieldToFilter(
                            'seller_id',
                            $id
                        );
                    if (count($model)) {
                        $productData['product']['sku'] = $product->getSku();
                        $productData['product'] = array_merge(
                            $product->getData(),
                            $productData['product']
                        );
                    } else {
                        return $this->getJsonResponse([
                            'error' => 1,
                            'message' => 'You are not authorised to edit this product.',
                        ]);
                    }
                } else {
                    return $this->getJsonResponse([
                        'error' => 1,
                        'message' => 'The Product does not exist for the provided Id.',
                    ]);
                }
            }

            if (isset($productData['product']['sku'])) {
                if (!$productData['product']['sku']) {
                    return $this->getJsonResponse([
                        'error' => 1,
                        'message' => 'SKU parameter is missing or empty.',
                    ]);
                }
                try {
                    $product = $this->productRepoInterface->get($productData['product']['sku']);
                    $model = $this->mpProductFactory->create()->getCollection()
                        ->addFieldToFilter(
                            'mageproduct_id',
                            $product->getId()
                        )->addFieldToFilter(
                            'seller_id',
                            $id
                        );

                    if (count($model)) {

                        $productData['id'] = $product->getId();
                        $productData['product'] = array_merge(
                            $product->getData(),
                            $productData['product']
                        );
                    } else {
                        return $this->getJsonResponse([
                            'error' => 1,
                            'message' => 'SKU already exists.',
                        ]);
                    }
                } catch (Exception $e) {
                    $this->mpHelper->logDataInLogger($e->getMessage());
                }
            }

            if (isset($productData['product']['store_id'])) {
                $productData['store'] = $productData['product']['store_id'];
            }

            if (isset($productData['product']['set'])) {
                $productData['set'] = '';
            }

            if (isset($productData['product']['type'])) {
                $productData['type'] = '';
            }

            if (isset($productData['product']['attribute_set_id'])) {
                $productData['set'] = $productData['product']['attribute_set_id'];
            }

            if (isset($productData['product']['type_id'])) {
                $productData['type'] = $productData['product']['type_id'];
            }

            if (isset($productData['product']['status'])) {
                $productData['status'] = $productData['product']['status'];
            }

            if (!isset($productData['product']['entity_id'])) {
                $productData['product']['stock_data']['manage_stock'] = 1;
                $productData['product']['stock_data']['use_config_manage_stock'] = 1;

                if (isset($productData['product']['media_gallery'])) {
                    $images = $productData['product']['media_gallery']['images'];
                    foreach ($images as $key => $value) {
                        // product_gallery_custom seems unused anywhere else
                        $productData['product_gallery_custom']['image'][$key] = $value;

                        // optional step for convenience, if no image assigned yet, assign this one for all 3 keys
                        $productData['product']['image'] = $productData['product']['image'] ?: $value[$key]['file'];
                        $productData['product']['small_image'] = $productData['product']['small_image'] ?: $value[$key]['file'];
                        $productData['product']['thumbnail'] = $productData['product']['thumbnail'] ?: $value[$key]['file'];
                    }
                }
            }

            $result = $this->saveProduct->saveProductData($id, $productData);
            if (!$result['error']) {
                if ($hasCommissions) {
                    $sku = $productData['product']['sku'];
                    $_product = $this->productRepoInterface->get($sku);

                    $_product->setPrice($productData['product']['price']);
                    $_product->setCustomAttribute('seller_price', $originalPrice);
                    $_product->setStoreId(0);
                    $this->productRepoInterface->save($_product);
                }
                $result['message'] = "Product Added Successfully";
            }

            return $this->getJsonResponse($result);
        } catch (Exception $e) {
            $this->mpHelper->logDataInLogger($e->getMessage());

            return $this->getJsonResponse([
                'error' => 1,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get seller sales details.
     *
     * @param $id
     * @return \Webkul\MpApi\Api\Magento\Framework\Api\SearchResults|\Webkul\MpApi\Model\Seller\Magento\Framework\Api\SearchResults
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getSellerSalesDetails($id)
    {
        $filters = array();
        $requestParams = $this->_request->getParams();
        $returnArray = [];
        $collectionOrders = $this->saleslistFactory->create()
            ->getCollection()
            ->addFieldToFilter('seller_id', $id)
            ->addFieldToFilter('magequantity', ['neq' => 0])
            ->addFieldToSelect('order_id')
            ->distinct(true);
        if ($collectionOrders->getSize() == 0) {
            throw new LocalizedException(
                __('Seller does not have any order')
            );
        }

        $orderIds = $collectionOrders->getData();

        $finalOrderIds = array_column($orderIds, 'order_id');

        $filters[] = $this->filterBuilder->setField("order_id")->setConditionType("in")->setValue(
            implode(',', $finalOrderIds)
        )->create();

        foreach ($requestParams as $key => $value) {
            if ($key == "created_at") {
                $filters[] = $this->filterBuilder->setField($key)->setConditionType("like")->setValue(
                    "%".$value."%"
                )->create();
            } elseif ($key == "created_from") {
                $dateFilter = "created_at";
                $filters[] = $this->filterBuilder->setField($dateFilter)->setConditionType("gteq")->setValue(
                    $value
                )->create();
            }
        }

        if (isset($requestParams['searchCriteria'])) {
            foreach ($requestParams['searchCriteria']['filter_groups'] as $searcfilterGroup) {
                foreach ($searcfilterGroup['filters'] as $searchFilter) {
                    $filters[] = $this->filterBuilder
                        ->setField($searchFilter['field'])
                        ->setConditionType($searchFilter['condition_type'])
                        ->setValue($searchFilter['value'])
                        ->create();
                }
            }
        }

        foreach ($filters as $filterData) {
            $filterGroup[] = $this->filterGroupBuilder->addFilter($filterData)->create();
        }

        $searchCriteria = $this->searchCriteriaBuilder->setFilterGroups($filterGroup)->create();
        $data = $this->ordersRepo->getLists($searchCriteria);

        return $data;
    }

    /**
     * Get seller sales list.
     *
     * @param $id
     * @return array|\Webkul\MpApi\Api\Magento\Framework\Api\SearchResults|\Webkul\MpApi\Model\Seller\Magento\Framework\Api\SearchResults
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getSellerSalesList($id)
    {
        $salesData = array();
        $filters = array();
        $requestParams = $this->_request->getParams();

        $filters[] = $this->filterBuilder->setField("seller_id")->setConditionType("eq")->setValue($id)->create();
        foreach ($requestParams as $key => $value) {
            if ($key == "order_id") {
                $filters[] = $this->filterBuilder->setField($key)->setConditionType("eq")->setValue($value)->create();
            } elseif ($key == "created_at") {
                $filters[] = $this->filterBuilder->setField($key)->setConditionType("like")->setValue(
                    "%".$value."%"
                )->create();
            } elseif ($key == "created_from") {
                $dateFilter = "created_at";
                $filters[] = $this->filterBuilder->setField($dateFilter)->setConditionType("gteq")->setValue(
                    $value
                )->create();
            }
        }

        foreach ($filters as $filterData) {
            $filterGroup[] = $this->filterGroupBuilder->addFilter($filterData)->create();
        }

        $searchCriteria = $this->searchCriteriaBuilder->setFilterGroups($filterGroup)->create();
        $data = $this->salesListRepo->getList($searchCriteria);

        $items = $data->getItems();
        if (count($items)) {
            foreach ($data->getItems() as $value) {
                $shipData = array();
                $billData = array();
                $orderData = array();
                $userData = array();

                $orderId = $value['order_id'];
                $order = $this->mageOrderRepo->get($orderId);

                $orderData['order_id'] = $orderId;
                $orderData['magerealorder_id'] = $value['magerealorder_id'];
                $orderData['seller_id'] = $value['seller_id'];
                $orderData['magebuyer_id'] = $value['magebuyer_id'];
                $orderData['mageproduct_id'] = $value['mageproduct_id'];
                $orderData['order_item_id'] = $value['order_item_id'];
                $orderData['parent_item_id'] = $value['parent_item_id'];
                $orderData['magequantity'] = $value['magequantity'];
                $orderData['magepro_name'] = $value['magepro_name'];
                $orderData['magepro_price'] = $value['magepro_price'];
                $orderData['total_amount'] = $value['total_amount'];
                $orderData['total_tax'] = $value['total_tax'];
                $orderData['total_commission'] = $value['total_commission'];
                $orderData['actual_seller_amount'] = $value['actual_seller_amount'];
                $orderData['created_at'] = $value['created_at'];
                $orderData['updated_at'] = $value['updated_at'];
                $orderData['is_coupon'] = $value['is_coupon'];
                $orderData['is_paid'] = $value['is_paid'];
                $orderData['commission_rate'] = $value['commission_rate'];
                $orderData['currency_rate'] = $value['currency_rate'];
                $orderData['applied_coupon_amount'] = $value['applied_coupon_amount'];

                $billingAddress = $order->getBillingAddress();
                if ($billingAddress) {
                    $Bstreet = $billingAddress->getStreet();

                    $billAddr = $Bstreet[0];
                    if (array_key_exists(1, $Bstreet)) {
                        $billAddr .= " ".$Bstreet[1];
                    }

                    $billData['f_name'] = $billingAddress->getFirstname();
                    $billData['l_name'] = $billingAddress->getLastname();
                    $billData['street_address'] = $billAddr;
                    $billData['Country'] = $billingAddress->getCountryId();
                    $billData['State'] = $billingAddress->getRegion();
                    $billData['City'] = $billingAddress->getCity();
                    $billData['Zip_code'] = $billingAddress->getPostcode();
                    $billData['phone_number'] = $billingAddress->getTelephone();

                    $userData['first_name'] = $billData['f_name'];
                    $userData['last_name'] = $billData['l_name'];
                    $userData['email'] = $this->sellerContract->getCustomerEmail(
                        (int)$order->getCustomerId(),
                        $order->getCustomerEmail()
                    );
                    $userData['phone'] = $billingAddress->getTelephone();

                    $orderData['user'] = $userData;
                    $orderData['billing_address'] = $billData;
                }

                $shippingAddress = $order->getShippingAddress();

                if ($shippingAddress) {
                    $shipStreet = $shippingAddress->getStreet();

                    $shipAddr = $shipStreet[0];
                    if (array_key_exists(1, $shipStreet)) {
                        $shipAddr .= " ".$shipStreet[1];
                    }

                    $shipData['f_name'] = $shippingAddress->getFirstname();
                    $shipData['l_name'] = $shippingAddress->getLastname();
                    $shipData['street_address'] = $shipAddr;
                    $shipData['Country'] = $shippingAddress->getCountryId();
                    $shipData['State'] = $shippingAddress->getRegion();
                    $shipData['City'] = $shippingAddress->getCity();
                    $shipData['Zip_code'] = $shippingAddress->getPostcode();
                    $shipData['phone_number'] = $shippingAddress->getTelephone();

                    $orderData['shipping_address'] = $shipData;
                }

                $salesData[] = $orderData;
            }

            return $salesData;
        } else {
            return $data;
        }
    }

    /**
     * Get order
     *
     * @param int $orderId
     * @return Magento\Sales\Model\Order
     * @throws NoSuchEntityException
     */
    private function getOrder($orderId)
    {
        $order = $this->mageOrderRepo->get($orderId);
        if ($order->getId()) {
            return $order;
        } else {
            throw NoSuchEntityException::singleField('orderId', $orderId);
        }
    }

    /**
     * View Invoice
     *
     * @param int $id
     * @param int $orderId
     * @param int $invoiceId
     * @return \Magento\Framework\Controller\Result\Json
     * @throws LocalizedException
     */
    public function viewInvoice($id, $orderId, $invoiceId)
    {
        try {
            if (!$this->isSeller($id)) {
                throw new LocalizedException(
                    __('Invalid Seller')
                );
            }
            $returnArray = [];
            $customerId = $id;
            $invoiceId = $invoiceId;
            $helper = $this->mpHelper;
            $order = $this->getOrder($orderId);
            $invoice = $this->invoiceRepository->get($invoiceId);
            $paymentCode = '';
            $payment_method = '';
            $orderId = $order->getId();
            if ($order->getPayment()) {
                $paymentCode = $order->getPayment()->getMethod();
                $payment_method = $order->getPayment()->getConfigData('title');
            }
            $invoiceStatus = '';
            if ($invoice->getState() == 1) {
                $invoiceStatus = __('Pending');
            } elseif ($invoice->getState() == 2) {
                $invoiceStatus = __('Paid');
            } elseif ($invoice->getState() == 3) {
                $invoiceStatus = __('Canceled');
            }
            $marketplaceOrders = $this->mpOrdersFactory
                ->create()
                ->getCollection()
                ->addFieldToFilter('order_id', $orderId)
                ->addFieldToFilter('seller_id', $customerId);

            if (count($marketplaceOrders)) {
                $returnArray['mainHeading'] = __('View Invoice Details');
                $returnArray['sendmailAction'] = __('Send Email To Customer');
                $returnArray['sendmailWarning'] = __('Are you sure you want to send order email to customer?');
                $returnArray['subHeading'] = __(
                    'Invoice #%1 - %2 | %3',
                    $invoice->getIncrementId(),
                    $invoiceStatus,
                    $invoice->getCreatedAt()
                );
                $returnArray['orderData']['title'] = __('Order Information');
                $returnArray['orderData']['label'] = __('Order # %1', $order->getIncrementId());
                $returnArray['orderData']['statusLabel'] = __('Order Status');
                $returnArray['orderData']['statusValue'] = ucfirst($order->getStatus());
                $returnArray['orderData']['dateLabel'] = __('Order Date');
                $returnArray['orderData']['dateValue'] = $order->getCreatedAt();
                // Buyer Data
                $returnArray['buyerData']['title'] = __('Buyer Information');
                $returnArray['buyerData']['nameLabel'] = __('Customer Name').': ';
                $returnArray['buyerData']['nameValue'] = $order->getCustomerName();
                $returnArray['buyerData']['emailLabel'] = __('Email').': ';
                $returnArray['buyerData']['emailValue'] = $this->sellerContract->getCustomerEmail(
                    (int)$order->getCustomerId(),
                    $order->getCustomerEmail()
                );
                // Shipping Address Data
                if (!$order->getIsVirtual()) {
                    $returnArray['shippingAddressData']['title'] = __('Shipping Address');
                    $shippingAddress = $order->getShippingAddress();
                    $shippingAddressData['name'] = $shippingAddress->getFirstname().' '.$shippingAddress->getLastname();
                    $shippingAddressData['street'] = $shippingAddress->getStreet()[0];
                    if (count($shippingAddress->getStreet()) > 1) {
                        if ($shippingAddress->getStreet()[1]) {
                            $shippingAddressData['street'] .= $shippingAddress->getStreet()[1];
                        }
                    }
                    $state = [
                        $shippingAddress->getCity(),
                        $shippingAddress->getRegion(),
                        $shippingAddress->getPostcode(),
                    ];
                    $shippingAddressData['state'] = implode(', ', $state);
                    $shippingAddressData['country'] = $this->country->create()
                        ->load($shippingAddress->getCountryId())->getName();
                    $shippingAddressData['telephone'] = 'T: '.$shippingAddress->getTelephone();
                    $returnArray['shippingAddressData']['address'][] = $shippingAddressData;

                    // Shipping Method Data
                    $returnArray['shippingMethodData']['title'] = __('Shipping Information');
                    if ($order->getShippingDescription()) {
                        $returnArray['shippingMethodData']['method'] = strip_tags($order->getShippingDescription());
                    } else {
                        $returnArray['shippingMethodData']['method'] = __('No shipping information available');
                    }
                }

                // Billing Address Data
                $returnArray['billingAddressData']['title'] = __('Billing Address');
                $billingAddress = $order->getBillingAddress();
                $billingAddressData['name'] = $billingAddress->getFirstname().' '.$billingAddress->getLastname();
                $billingAddressData['street'] = $billingAddress->getStreet()[0];
                if (count($billingAddress->getStreet()) > 1) {
                    if ($billingAddress->getStreet()[1]) {
                        $billingAddressData['street'] .= $billingAddress->getStreet()[1];
                    }
                }
                $billState = [
                    $billingAddress->getCity(),
                    $billingAddress->getRegion(),
                    $billingAddress->getPostcode(),
                ];
                $billingAddressData['state'] = implode(', ', $billState);
                $billingAddressData['country'] = $this->country->create()
                    ->load($billingAddress->getCountryId())
                    ->getName();
                $billingAddressData['telephone'] = 'T: '.$billingAddress->getTelephone();
                $returnArray['billingAddressData']['address'][] = $billingAddressData;

                // Payment Method Data
                $returnArray['paymentMethodData']['title'] = __('Payment Method');
                $returnArray['paymentMethodData']['method'] = $order->getPayment()->getMethodInstance()->getTitle();

                // Item List
                $itemCollection = $order->getAllVisibleItems();
                $_count = count($itemCollection);
                $subtotal = 0;
                $vendorSubtotal = 0;
                $totaltax = 0;
                $adminSubtotal = 0;
                $shippingamount = 0;
                $codchargesTotal = 0;

                foreach ($itemCollection as $_item) {
                    $eachItem = [];
                    $rowTotal = 0;
                    $availableSellerItem = 0;
                    $shippingcharges = 0;
                    $itemPrice = 0;
                    $sellerItemCost = 0;
                    $totaltaxPeritem = 0;
                    $codchargesPeritem = 0;
                    $sellerItemCommission = 0;
                    $sellerOrderslist = $this->saleslistFactory
                        ->create()->getCollection()
                        ->addFieldToFilter('seller_id', $customerId)
                        ->addFieldToFilter('order_id', $orderId)
                        ->addFieldToFilter('mageproduct_id', $_item->getProductId())
                        ->addFieldToFilter('order_item_id', $_item->getItemId())
                        ->setOrder('order_id', 'DESC');

                    foreach ($sellerOrderslist as $sellerItem) {
                        $availableSellerItem = 1;
                        $totalamount = $sellerItem->getTotalAmount();
                        $sellerItemCost = $sellerItem->getActualSellerAmount();
                        $sellerItemCommission = $sellerItem->getTotalCommision();
                        $shippingcharges = $sellerItem->getShippingCharges();
                        $itemPrice = $sellerItem->getMageproPrice();
                        $totaltaxPeritem = $sellerItem->getTotalTax();
                        $codchargesPeritem = $sellerItem->getCodCharges();
                    }
                    if ($availableSellerItem == 1) {
                        $sellerItemQty = $_item->getQtyOrdered();
                        $rowTotal = $itemPrice * $sellerItemQty;
                        $vendorSubtotal = $vendorSubtotal + $sellerItemCost;
                        $subtotal = $subtotal + $rowTotal;
                        $adminSubtotal = $adminSubtotal + $sellerItemCommission;
                        $totaltax = $totaltax + $totaltaxPeritem;
                        $codchargesTotal = $codchargesTotal + $codchargesPeritem;
                        $shippingamount = $shippingamount + $shippingcharges;
                        $result = [];
                        if ($options = $_item->getProductOptions()) {
                            $result = $this->setResultValue($options, $result);
                        }
                        $eachItem['productName'] = $_item->getName();
                        $eachItem['productId'] = $_item->getProductId();
                        if ($options = $result) {
                            $eachItem = $this->setEachItemOption($options, $eachItem);
                        }
                        $eachItem['price'] = strip_tags($order->formatPrice($_item->getPrice()));
                        $eachItem['qty']['Ordered'] = $_item->getQtyOrdered() * 1;
                        $eachItem['qty']['Invoiced'] = $_item->getQtyInvoiced() * 1;
                        $eachItem['qty']['Shipped'] = $_item->getQtyShipped() * 1;
                        $eachItem['qty']['Canceled'] = $_item->getQtyCanceled() * 1;
                        $eachItem['qty']['Refunded'] = $_item->getQtyRefunded() * 1;
                        $eachItem['subTotal'] = strip_tags($order->formatPrice($rowTotal));
                        if ($paymentCode == 'mpcashondelivery') {
                            $eachItem['codCharges'] = strip_tags($order->formatPrice($codchargesPeritem));
                        }
                        $eachItem['adminComission'] = strip_tags($order->formatPrice($sellerItemCommission));
                        $eachItem['vendorTotal'] = strip_tags($order->formatPrice($sellerItemCost));
                        $returnArray['items'][] = $eachItem;
                    }
                }
                $returnArray['subtotal']['title'] = __('Subtotal');
                $returnArray['subtotal']['value'] = strip_tags($order->formatPrice($subtotal));
                $returnArray['shipping']['title'] = __('Shipping & Handling');
                $returnArray['shipping']['value'] = strip_tags($order->formatPrice($shippingamount));
                $returnArray['tax']['title'] = __('Total Tax');
                $returnArray['tax']['value'] = strip_tags($order->formatPrice($totaltax));
                $admintotaltax = 0;
                $vendortotaltax = 0;
                if (!$this->mpHelper->getConfigTaxManage()) {
                    $admintotaltax = $totaltax;
                } else {
                    $vendortotaltax = $totaltax;
                }
                if ($paymentCode == 'mpcashondelivery') {
                    $returnArray['cod']['title'] = __('Total COD Charges');
                    $returnArray['cod']['value'] = strip_tags($order->formatPrice($codchargesTotal));
                }
                $returnArray['totalOrderedAmount']['title'] = __('Total Ordered Amount');
                $returnArray['totalOrderedAmount']['value'] = strip_tags(
                    $order->formatPrice(
                        $subtotal + $shippingamount + $codchargesTotal + $totaltax
                    )
                );
                $returnArray['totalVendorAmount']['title'] = __('Total Vendor Amount');
                $returnArray['totalVendorAmount']['value'] = strip_tags(
                    $order->formatPrice(
                        $vendorSubtotal + $shippingamount + $codchargesTotal + $vendortotaltax
                    )
                );
                $returnArray['totalAdminComission']['title'] = __('Total Admin Commission');
                $returnArray['totalAdminComission']['value'] = strip_tags(
                    $order->formatPrice(
                        $adminSubtotal + $admintotaltax
                    )
                );
                $returnArray['status'] = self::SUCCESS;
            } else {
                throw new LocalizedException(
                    __('Invalid Request')
                );
            }

            return $this->getJsonResponse($returnArray);
        } catch (LocalizedException $e) {
            $returnArray['message'] = $e->getMessage();
            $returnArray['status'] = self::LOCAL_ERROR;

            return $this->getJsonResponse($returnArray);
        } catch (Exception $e) {
            $this->mpHelper->logDataInLogger($e);
            $returnArray['status'] = self::SEVERE_ERROR;
            $returnArray['message'] = __('Invalid Request');

            return $this->getJsonResponse($returnArray);
        }
    }

    /**
     * @param array $productData
     * @param int $sellerId
     * @return bool
     */
    private function isSellerValid(array $productData, int $sellerId): bool
    {
        if (isset($productData['product']['assign_seller']['seller_id']) &&
            trim($productData['product']['assign_seller']['seller_id']) == $sellerId
        ) {
            return true;
        }

        return false;
    }
}
