<?xml version="1.0"?>
<!-- 
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Catalog\Model\Product\CopyConstructor\Composite">
        <arguments>
            <argument name="constructors" xsi:type="array">
                <item name="crossSell" xsi:type="string">Magento\Catalog\Model\Product\CopyConstructor\CrossSell</item>
                <item name="upSell" xsi:type="string">Magento\Catalog\Model\Product\CopyConstructor\UpSell</item>
                <item name="related" xsi:type="string">Magento\Catalog\Model\Product\CopyConstructor\Related</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Catalog\Model\Product\Copier">
        <arguments>
            <argument name="copyConstructor" xsi:type="object">Magento\Catalog\Model\Product\CopyConstructor\Composite</argument>
        </arguments>
    </type>

    <!-- Register Admin JWT User Context -->
    <type name="Magento\Authorization\Model\CompositeUserContext">
        <arguments>
            <argument name="userContexts" xsi:type="array">
                <item name="adminJwtUserContext" xsi:type="array">
                    <item name="type" xsi:type="object">Webkul\MpApi\Model\Authorization\AdminJwtUserContext</item>
                    <item name="sortOrder" xsi:type="string">10</item>
                </item>
            </argument>
        </arguments>
    </type>
</config>
