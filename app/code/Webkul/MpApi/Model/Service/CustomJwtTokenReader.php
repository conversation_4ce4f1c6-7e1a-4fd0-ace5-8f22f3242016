<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
declare(strict_types=1);

namespace Webkul\MpApi\Model\Service;

use Magento\Authorization\Model\UserContextInterface;
use Magento\Framework\Jwt\Payload\ClaimsPayload;
use Magento\Integration\Api\Data\UserToken;
use Magento\Integration\Api\Exception\UserTokenException;
use Magento\Integration\Api\UserTokenReaderInterface;
use Magento\JwtUserToken\Model\Data\Header;
use Magento\JwtUserToken\Model\Data\JwtTokenData;
use Magento\JwtUserToken\Model\Data\JwtUserContext;
use Magento\User\Model\UserFactory;
use Magento\User\Model\ResourceModel\User as UserResource;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResource;
use Psr\Log\LoggerInterface;

/**
 * Custom JWT Token Reader for admin and customer tokens with utypid field
 */
class CustomJwtTokenReader implements UserTokenReaderInterface
{
    /**
     * @param UserFactory $userFactory
     * @param UserResource $userResource
     * @param CustomerFactory $customerFactory
     * @param CustomerResource $customerResource
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly UserFactory $userFactory,
        private readonly UserResource $userResource,
        private readonly CustomerFactory $customerFactory,
        private readonly CustomerResource $customerResource,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Read and validate custom JWT token
     *
     * @param string $token
     * @return UserToken
     * @throws UserTokenException
     */
    public function read(string $token): UserToken
    {
        try {
            $tokenData = $this->decodeJwtToken($token);
            
            if (!$tokenData) {
                throw new UserTokenException('Invalid JWT token');
            }

            $userId = $tokenData['uid'] ?? null;
            $userTypeId = $tokenData['utypid'] ?? null;
            
            if (!$userId || !$userTypeId) {
                throw new UserTokenException('Missing user ID or type in token');
            }

            // Handle admin tokens (utypid: 1)
            if ($userTypeId == 1) {
                if (!$this->validateAdminUser($userId)) {
                    throw new UserTokenException('Invalid admin user');
                }
                
                $this->logger->info("Custom JWT: Admin user {$userId} authenticated");
                
                return $this->createUserToken(
                    (int)$userId,
                    UserContextInterface::USER_TYPE_ADMIN,
                    $tokenData
                );
            }
            
            // Handle customer tokens (utypid: 3)
            if ($userTypeId == 3) {
                if (!$this->validateCustomerUser($userId)) {
                    throw new UserTokenException('Invalid customer user');
                }
                
                $this->logger->info("Custom JWT: Customer user {$userId} authenticated");
                
                return $this->createUserToken(
                    (int)$userId,
                    UserContextInterface::USER_TYPE_CUSTOMER,
                    $tokenData
                );
            }

            throw new UserTokenException('Unsupported user type');

        } catch (\Exception $e) {
            $this->logger->error('Custom JWT token validation error: ' . $e->getMessage());
            throw new UserTokenException('Token validation failed');
        }
    }

    /**
     * Decode JWT token
     *
     * @param string $token
     * @return array|null
     */
    private function decodeJwtToken(string $token): ?array
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            // Decode the payload (second part)
            $payload = $parts[1];
            
            // Add padding if needed
            $payload .= str_repeat('=', (4 - strlen($payload) % 4) % 4);
            
            $decoded = base64_decode($payload);
            if (!$decoded) {
                return null;
            }

            $data = json_decode($decoded, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }

            // Check token expiration
            if (isset($data['exp']) && $data['exp'] < time()) {
                $this->logger->info('Custom JWT token expired');
                return null;
            }

            return $data;

        } catch (\Exception $e) {
            $this->logger->error('Custom JWT decode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate admin user exists and is active
     *
     * @param int $userId
     * @return bool
     */
    private function validateAdminUser(int $userId): bool
    {
        try {
            $user = $this->userFactory->create();
            $this->userResource->load($user, $userId);

            if (!$user->getId()) {
                return false;
            }

            // Check if user is active
            if (!$user->getIsActive()) {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Admin user validation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate customer user exists and is active
     *
     * @param int $userId
     * @return bool
     */
    private function validateCustomerUser(int $userId): bool
    {
        try {
            $customer = $this->customerFactory->create();
            $this->customerResource->load($customer, $userId);

            if (!$customer->getId()) {
                return false;
            }

            // Check if customer is active
            if ($customer->getIsActive() === '0') {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Customer user validation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create UserToken instance
     *
     * @param int $userId
     * @param int $userType
     * @param array $tokenData
     * @return UserToken
     */
    private function createUserToken(int $userId, int $userType, array $tokenData): UserToken
    {
        $iat = \DateTimeImmutable::createFromFormat('U', (string)($tokenData['iat'] ?? time()));
        $exp = \DateTimeImmutable::createFromFormat('U', (string)($tokenData['exp'] ?? time() + 3600));

        return new UserToken(
            new JwtUserContext($userId, $userType),
            new JwtTokenData($iat, $exp, new Header([]), new ClaimsPayload([]))
        );
    }
}
