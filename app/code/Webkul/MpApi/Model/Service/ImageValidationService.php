<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
declare(strict_types=1);

namespace Webkul\MpApi\Model\Service;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\RequestInterface;

/**
 * Service class for validating image uploads
 */
class ImageValidationService
{
    /**
     * Constructor
     *
     * @param RequestInterface $request
     */
    public function __construct(
        private readonly RequestInterface $request
    ) {
    }

    /**
     * Validate upload request and extract images
     *
     * @return array
     * @throws LocalizedException
     */
    public function validateUploadRequest(): array
    {
        $files = $this->getUploadedFiles();
        
        if ($this->request->getMethod() !== "POST" || empty($files)) {
            throw new LocalizedException(__("Invalid Request."));
        }

        if (!isset($files['images']) || !is_array($files['images'])) {
            throw new LocalizedException(__("No images found in request."));
        }

        $images = $files['images'];
        $imageCount = count($images);

        if ($imageCount < ImageUploadConfig::MIN_IMAGE_COUNT || $imageCount > ImageUploadConfig::MAX_IMAGE_COUNT) {
            throw new LocalizedException(
                __('Please upload between %1 and %2 images.',
                   ImageUploadConfig::MIN_IMAGE_COUNT,
                   ImageUploadConfig::MAX_IMAGE_COUNT)
            );
        }

        return $images;
    }

    /**
     * Validate all image files
     *
     * @param array $images
     * @throws LocalizedException
     */
    public function validateImageFiles(array $images): void
    {
        $totalSize = 0;

        foreach ($images as $index => $image) {
            $this->validateSingleImage($image, $index);
            $totalSize += $image['size'];
        }

        if ($totalSize > ImageUploadConfig::MAX_TOTAL_FILE_SIZE) {
            throw new LocalizedException(
                __('Total file size exceeds maximum limit of %1MB.',
                   round(ImageUploadConfig::MAX_TOTAL_FILE_SIZE / ImageUploadConfig::BYTES_TO_MB_FACTOR))
            );
        }
    }

    /**
     * Validate a single image file
     *
     * @param array $image
     * @param int $index
     * @throws LocalizedException
     */
    public function validateSingleImage(array $image, int $index): void
    {
        if (!isset($image['error']) || $image['error'] !== UPLOAD_ERR_OK) {
            throw new LocalizedException(__('Upload error for image %1.', $index + 1));
        }

        if (!isset($image['size']) || $image['size'] > ImageUploadConfig::MAX_INDIVIDUAL_FILE_SIZE) {
            throw new LocalizedException(
                __('Image %1 exceeds maximum size of %2MB.',
                   $index + 1,
                   round(ImageUploadConfig::MAX_INDIVIDUAL_FILE_SIZE / ImageUploadConfig::BYTES_TO_MB_FACTOR))
            );
        }

        if (!isset($image['name'])) {
            throw new LocalizedException(__('Missing filename for image %1.', $index + 1));
        }

        $fileName = $image['name'];
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        if (!in_array($fileExtension, ImageUploadConfig::ALLOWED_EXTENSIONS)) {
            throw new LocalizedException(
                __('Invalid file type for image %1. Allowed types: %2',
                   $index + 1,
                   implode(', ', ImageUploadConfig::ALLOWED_EXTENSIONS))
            );
        }

        if (!isset($image['tmp_name']) || !$this->isValidImageFile($image['tmp_name'])) {
            throw new LocalizedException(__('File %1 is not a valid image.', $index + 1));
        }
    }

    /**
     * Check if uploaded file is a valid image
     *
     * @param string $tmpName
     * @return bool
     */
    public function isValidImageFile(string $tmpName): bool
    {
        if (!file_exists($tmpName)) {
            return false;
        }

        $imageInfo = getimagesize($tmpName);
        return $imageInfo !== false;
    }

    /**
     * Get uploaded files from request
     * This method encapsulates access to $_FILES superglobal
     *
     * @return array
     */
    private function getUploadedFiles(): array
    {
        return $this->request->getFiles()->toArray();
    }
}
