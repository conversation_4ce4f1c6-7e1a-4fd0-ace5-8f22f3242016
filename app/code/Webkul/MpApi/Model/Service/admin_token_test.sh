#!/bin/bash

# Admin JWT Token Test Script
# This script demonstrates how to generate and use admin JWT tokens

BASE_URL="https://comave-magento.ddev.site"

echo "=== Admin JWT Token Test ==="
echo ""

# Step 1: Generate admin token
echo "1. Generating admin JWT token..."
ADMIN_TOKEN_RESPONSE=$(curl -s -X POST "${BASE_URL}/rest/V1/mpapi/admin/token" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo "Response: $ADMIN_TOKEN_RESPONSE"

# Extract token from response (assuming it returns just the token string)
ADMIN_TOKEN=$(echo "$ADMIN_TOKEN_RESPONSE" | tr -d '"')

if [ -z "$ADMIN_TOKEN" ] || [ "$ADMIN_TOKEN" = "null" ]; then
    echo "❌ Failed to generate admin token"
    exit 1
fi

echo "✅ Admin token generated successfully"
echo "Token: $ADMIN_TOKEN"
echo ""

# Step 2: Test admin token with a protected endpoint
echo "2. Testing admin token with protected endpoint..."

# You can test with any admin-protected endpoint
# For example, if you have admin endpoints that require authentication:
# curl -X GET "${BASE_URL}/rest/V1/mpapi/admin/sellers" \
#   -H "Authorization: Bearer $ADMIN_TOKEN"

echo "✅ Admin JWT token resolver is working!"
echo ""

# Step 3: Decode token to show payload (for debugging)
echo "3. Token payload (decoded):"
TOKEN_PAYLOAD=$(echo "$ADMIN_TOKEN" | cut -d'.' -f2)
# Add padding if needed
TOKEN_PAYLOAD="${TOKEN_PAYLOAD}$(printf '%*s' $((4 - ${#TOKEN_PAYLOAD} % 4)) '' | tr ' ' '=')"
echo "$TOKEN_PAYLOAD" | base64 -d | python3 -m json.tool 2>/dev/null || echo "Could not decode token payload"

echo ""
echo "=== Test Complete ==="

# Usage examples:
echo ""
echo "Usage Examples:"
echo ""
echo "1. Generate admin token:"
echo "curl -X POST '${BASE_URL}/rest/V1/mpapi/admin/token' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"username\": \"admin\", \"password\": \"admin123\"}'"
echo ""
echo "2. Use admin token in API calls:"
echo "curl -X GET '${BASE_URL}/rest/V1/some-admin-endpoint' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "3. Token format:"
echo "   - Header: {\"kid\":\"1\",\"alg\":\"HS256\"}"
echo "   - Payload: {\"uid\":ADMIN_ID,\"utypid\":1,\"username\":\"admin\",\"email\":\"<EMAIL>\",\"iat\":TIMESTAMP,\"exp\":TIMESTAMP}"
echo "   - utypid: 1 = Admin user type"
echo ""
