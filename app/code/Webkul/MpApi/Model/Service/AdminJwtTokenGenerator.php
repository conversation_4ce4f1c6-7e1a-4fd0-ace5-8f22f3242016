<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */

namespace Webkul\MpApi\Model\Service;

use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\Exception\LocalizedException;
use Magento\User\Model\UserFactory;
use Magento\User\Model\ResourceModel\User as UserResource;
use Magento\Framework\Encryption\EncryptorInterface;
use Psr\Log\LoggerInterface;

/**
 * Admin JWT Token Generator Service
 */
class AdminJwtTokenGenerator
{
    /**
     * @var UserFactory
     */
    private $userFactory;

    /**
     * @var UserResource
     */
    private $userResource;

    /**
     * @var EncryptorInterface
     */
    private $encryptor;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * JWT Secret Key (should be configurable in production)
     */
    private const JWT_SECRET = 'your-secret-key-here';

    /**
     * Token expiration time (24 hours)
     */
    private const TOKEN_EXPIRATION = 86400;

    /**
     * Constructor
     *
     * @param UserFactory $userFactory
     * @param UserResource $userResource
     * @param EncryptorInterface $encryptor
     * @param LoggerInterface $logger
     */
    public function __construct(
        UserFactory $userFactory,
        UserResource $userResource,
        EncryptorInterface $encryptor,
        LoggerInterface $logger
    ) {
        $this->userFactory = $userFactory;
        $this->userResource = $userResource;
        $this->encryptor = $encryptor;
        $this->logger = $logger;
    }

    /**
     * Generate JWT token for admin user
     *
     * @param string $username
     * @param string $password
     * @return string
     * @throws AuthenticationException
     * @throws LocalizedException
     */
    public function generateToken($username, $password)
    {
        $user = $this->authenticateAdmin($username, $password);
        
        if (!$user || !$user->getId()) {
            throw new AuthenticationException(__('Invalid admin credentials'));
        }

        return $this->createJwtToken($user);
    }

    /**
     * Authenticate admin user
     *
     * @param string $username
     * @param string $password
     * @return \Magento\User\Model\User|null
     * @throws LocalizedException
     */
    private function authenticateAdmin($username, $password)
    {
        try {
            $user = $this->userFactory->create();
            $this->userResource->load($user, $username, 'username');

            if (!$user->getId()) {
                return null;
            }

            if (!$user->getIsActive()) {
                throw new AuthenticationException(__('Admin account is disabled'));
            }

            // Verify password
            if (!$this->encryptor->validateHash($password, $user->getPassword())) {
                return null;
            }

            return $user;

        } catch (\Exception $e) {
            $this->logger->error('Admin authentication error: ' . $e->getMessage());
            throw new LocalizedException(__('Authentication failed'));
        }
    }

    /**
     * Create JWT token for admin user
     *
     * @param \Magento\User\Model\User $user
     * @return string
     */
    private function createJwtToken($user)
    {
        $header = [
            'kid' => '1',
            'alg' => 'HS256'
        ];

        $payload = [
            'uid' => (int)$user->getId(),
            'utypid' => 1, // Admin user type
            'username' => $user->getUsername(),
            'email' => $user->getEmail(),
            'iat' => time(),
            'exp' => time() + self::TOKEN_EXPIRATION
        ];

        $headerEncoded = $this->base64UrlEncode(json_encode($header));
        $payloadEncoded = $this->base64UrlEncode(json_encode($payload));
        
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, self::JWT_SECRET, true);
        $signatureEncoded = $this->base64UrlEncode($signature);

        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    /**
     * Base64 URL encode
     *
     * @param string $data
     * @return string
     */
    private function base64UrlEncode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Validate JWT token
     *
     * @param string $token
     * @return array|null
     */
    public function validateToken($token)
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            [$headerEncoded, $payloadEncoded, $signatureEncoded] = $parts;

            // Verify signature
            $expectedSignature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, self::JWT_SECRET, true);
            $expectedSignatureEncoded = $this->base64UrlEncode($expectedSignature);

            if (!hash_equals($expectedSignatureEncoded, $signatureEncoded)) {
                return null;
            }

            // Decode payload
            $payload = json_decode(base64_decode(strtr($payloadEncoded, '-_', '+/')), true);
            
            if (!$payload || json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }

            // Check expiration
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return null;
            }

            return $payload;

        } catch (\Exception $e) {
            $this->logger->error('JWT validation error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate token for admin user by ID
     *
     * @param int $userId
     * @return string
     * @throws LocalizedException
     */
    public function generateTokenByUserId($userId)
    {
        $user = $this->userFactory->create();
        $this->userResource->load($user, $userId);

        if (!$user->getId()) {
            throw new LocalizedException(__('Admin user not found'));
        }

        if (!$user->getIsActive()) {
            throw new LocalizedException(__('Admin account is disabled'));
        }

        return $this->createJwtToken($user);
    }
}
