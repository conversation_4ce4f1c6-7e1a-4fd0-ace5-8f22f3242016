# Clear cache and test compilation
php bin/magento cache:clean
php bin/magento cache:flush
php bin/magento setup:di:compile

echo "=== ADMIN-ONLY UPLOAD ENDPOINT TEST ==="
echo "This endpoint now requires admin authentication only!"
echo ""

# Test with admin token - you'll need to get a proper admin token first
echo "To get admin token, use:"
echo "curl -X POST 'https://comave-magento.ddev.site/rest/V1/integration/admin/token' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"username\": \"admin\", \"password\": \"admin123\"}'"
echo ""

echo "Then test upload with admin token:"
echo "curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -F 'images[]=@image1.png' \\"
echo "  -F 'images[]=@image2.png'"

# Alternative: Test with a fresh JWT token if the above expires
# Get a new token first:
# curl -X POST 'https://comave-magento.ddev.site/rest/V1/integration/customer/token' \
#   -H 'Content-Type: application/json' \
#   -d '{"username": "<EMAIL>", "password": "password"}'
