# Clear cache and test compilation
php bin/magento cache:clean
php bin/magento cache:flush
php bin/magento setup:di:compile

echo "Testing with admin JWT token (utypid: 1)..."
echo "Admin token will be treated as customer for webapi compatibility..."

curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \
  -H 'Authorization: Bearer eyJraWQiOiIxIiwiYWxnIjoiSFMyNTYifQ.*******************************************************************************************************************************************************.ApdhcCG-_Jr_tKh6b0HqTqZXGmCjkilYGnAsMoleHcw' \
  -F 'images[]=@image1.png' \
  -F 'images[]=@image2.png'

echo ""
echo "Check var/log/system.log for JWT authentication debug messages"
echo "The custom JWT user context now handles both admin (utypid: 1) and customer (utypid: 3) tokens!"

# Alternative: Test with a fresh JWT token if the above expires
# Get a new token first:
# curl -X POST 'https://comave-magento.ddev.site/rest/V1/integration/customer/token' \
#   -H 'Content-Type: application/json' \
#   -d '{"username": "<EMAIL>", "password": "password"}'
