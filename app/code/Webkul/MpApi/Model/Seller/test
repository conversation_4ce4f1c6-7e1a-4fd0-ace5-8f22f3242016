# Clear cache and test compilation
php bin/magento cache:clean
php bin/magento cache:flush
php bin/magento setup:di:compile

echo "=== ADMIN-ONLY UPLOAD ENDPOINT TEST ==="
echo "Using proper Magento JWT token reader system!"
echo ""

echo "Testing with your custom admin JWT token (utypid: 1):"
curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \
  -H 'Authorization: Bearer eyJraWQiOiIxIiwiYWxnIjoiSFMyNTYifQ.*******************************************************************************************************************************************************.ApdhcCG-_Jr_tKh6b0HqTqZXGmCjkilYGnAsMoleHcw' \
  -F 'images[]=@image1.png' \
  -F 'images[]=@image2.png'

echo ""
echo "✅ Custom JWT Token Reader now handles your admin tokens properly!"
echo "✅ Uses Magento's CompositeTokenReader system"
echo "✅ Validates admin users and enforces ACL permissions"

# Alternative: Test with a fresh JWT token if the above expires
# Get a new token first:
# curl -X POST 'https://comave-magento.ddev.site/rest/V1/integration/customer/token' \
#   -H 'Content-Type: application/json' \
#   -d '{"username": "<EMAIL>", "password": "password"}'
