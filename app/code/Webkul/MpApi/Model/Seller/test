# Clear cache and test compilation
php bin/magento cache:clean
php bin/magento cache:flush
php bin/magento setup:di:compile

echo "=== ADMIN-ONLY UPLOAD ENDPOINT TEST ==="
echo "Following the same pattern as existing admin endpoints!"
echo ""

echo "Step 1: Get standard Magento admin token"
echo "Trying with your credentials..."
ADMIN_TOKEN=$(curl -s -X POST 'https://comave-magento.ddev.site/rest/V1/integration/admin/token' \
  -H 'Content-Type: application/json' \
  -d '{"username": "<EMAIL>", "password": "2012y2012"}' | tr -d '"')

if [ -z "$ADMIN_TOKEN" ] || [ "$ADMIN_TOKEN" = "null" ]; then
    echo "Trying with default admin credentials..."
    ADMIN_TOKEN=$(curl -s -X POST 'https://comave-magento.ddev.site/rest/V1/integration/admin/token' \
      -H 'Content-Type: application/json' \
      -d '{"username": "admin", "password": "admin123"}' | tr -d '"')
fi

if [ ! -z "$ADMIN_TOKEN" ] && [ "$ADMIN_TOKEN" != "null" ]; then
    echo "✅ Standard admin token: $ADMIN_TOKEN"

    echo ""
    echo "Step 2: Test upload with standard admin token"
    curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \
      -H "Authorization: Bearer $ADMIN_TOKEN" \
      -F 'images[]=@image1.png' \
      -F 'images[]=@image2.png'
else
    echo "❌ Failed to get standard admin token"
fi

echo ""
echo ""
echo "Step 3: Test with your custom JWT token (if it works with existing system)"
curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \
  -H 'Authorization: Bearer eyJraWQiOiIxIiwiYWxnIjoiSFMyNTYifQ.*******************************************************************************************************************************************************.ApdhcCG-_Jr_tKh6b0HqTqZXGmCjkilYGnAsMoleHcw' \
  -F 'images[]=@image1.png' \
  -F 'images[]=@image2.png'

# Alternative: Test with a fresh JWT token if the above expires
# Get a new token first:
# curl -X POST 'https://comave-magento.ddev.site/rest/V1/integration/customer/token' \
#   -H 'Content-Type: application/json' \
#   -d '{"username": "<EMAIL>", "password": "password"}'


****************************************

curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \
  -H 'Authorization: Bearer eyJraWQiOiIxIiwiYWxnIjoiSFMyNTYifQ.eyJ1aWQiOjMxLCJ1dHlwaWQiOjIsImlhdCI6MTc1MzA5MzE3NSwiZXhwIjoxNzUzMTI5MTc1fQ.ifi12S7CvE1oJ9W9boKkAcelS4s4MBDXkLjpi8XiZ3o' \
  -F 'images[]=@image1.png' \
  -F 'images[]=@image2.png'