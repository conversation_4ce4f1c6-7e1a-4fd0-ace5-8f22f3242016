# Step 1: Get a proper Magento customer token first
# Replace with actual customer credentials
curl -X POST 'https://comave-magento.ddev.site/rest/V1/integration/customer/token' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "<EMAIL>",
    "password": "customer_password"
  }'

# Step 2: Use the returned token in the upload request
# Replace YOUR_CUSTOMER_TOKEN with the token from step 1
curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \
  -H 'Authorization: Bearer YOUR_CUSTOMER_TOKEN' \
  -F 'images[]=@image1.png' \
  -F 'images[]=@image2.png'
