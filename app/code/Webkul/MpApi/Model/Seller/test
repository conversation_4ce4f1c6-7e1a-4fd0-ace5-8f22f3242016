# Clear cache and test compilation
php bin/magento cache:clean
php bin/magento cache:flush
php bin/magento setup:di:compile

# Test the upload endpoint with your JWT token
curl -X POST 'https://comave-magento.ddev.site/rest/V1/mpapi/uploadmultipleimages' \
  -H 'Authorization: Bearer eyJraWQiOiIxIiwiYWxnIjoiSFMyNTYifQ.eyJ1aWQiOjEzODA4LCJ1dHlwaWQiOjMsImlhdCI6MTc1Mjk0NzA1MSwiZXhwIjoxNzUyOTgzMDUxfQ.M4EzLMJpcglPgwKlJ4dWJAiwq_tdEH4pMYZYXZkkLTE' \
  -F 'images[]=@image1.png' \
  -F 'images[]=@image2.png'

# Alternative: Test with a fresh JWT token if the above expires
# Get a new token first:
# curl -X POST 'https://comave-magento.ddev.site/rest/V1/integration/customer/token' \
#   -H 'Content-Type: application/json' \
#   -d '{"username": "<EMAIL>", "password": "password"}'
