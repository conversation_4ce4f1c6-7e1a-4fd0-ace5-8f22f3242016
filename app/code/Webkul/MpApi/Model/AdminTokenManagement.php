<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */

namespace Webkul\MpApi\Model;

use Webkul\MpApi\Api\AdminTokenManagementInterface;
use Webkul\MpApi\Model\Service\AdminJwtTokenGenerator;
use Magento\Framework\Exception\AuthenticationException;
use Magento\Framework\Exception\LocalizedException;

/**
 * Admin Token Management Implementation
 */
class AdminTokenManagement implements AdminTokenManagementInterface
{
    /**
     * @var AdminJwtTokenGenerator
     */
    private $tokenGenerator;

    /**
     * Constructor
     *
     * @param AdminJwtTokenGenerator $tokenGenerator
     */
    public function __construct(
        AdminJwtTokenGenerator $tokenGenerator
    ) {
        $this->tokenGenerator = $tokenGenerator;
    }

    /**
     * Generate admin JWT token
     *
     * @param string $username
     * @param string $password
     * @return string
     * @throws AuthenticationException
     * @throws LocalizedException
     */
    public function createAdminAccessToken($username, $password)
    {
        return $this->tokenGenerator->generateToken($username, $password);
    }

    /**
     * Validate admin JWT token
     *
     * @param string $token
     * @return array|null
     */
    public function validateAdminToken($token)
    {
        return $this->tokenGenerator->validateToken($token);
    }
}
