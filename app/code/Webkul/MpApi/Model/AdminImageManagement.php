<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
declare(strict_types=1);

namespace Webkul\MpApi\Model;

use Webkul\MpApi\Api\AdminImageManagementInterface;
use Webkul\MpApi\Model\Service\ImageValidationService;
use Webkul\MpApi\Model\Service\ImageUploadProcessor;
use Webkul\Marketplace\Helper\Data as MpHelper;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Controller\Result\JsonFactory;

/**
 * Admin Image Management implementation
 */
class AdminImageManagement implements AdminImageManagementInterface
{
    /**
     * @var array
     */
    private array $returnArrayData = [];

    /**
     * Constructor
     *
     * @param ImageValidationService $imageValidationService
     * @param ImageUploadProcessor $imageUploadProcessor
     * @param MpHelper $mpHelper
     * @param JsonFactory $jsonFactory
     */
    public function __construct(
        private readonly ImageValidationService $imageValidationService,
        private readonly ImageUploadProcessor $imageUploadProcessor,
        private readonly MpHelper $mpHelper,
        private readonly JsonFactory $jsonFactory
    ) {
        $this->returnArrayData["success"] = false;
        $this->returnArrayData["message"] = "log";
    }

    /**
     * Upload Multiple Images (1-4 images, max 20MB total)
     * Admin-only endpoint
     *
     * @return string
     */
    public function uploadMultipleImages()
    {
        try {
            $this->mpHelper->logDataInLogger("Admin multiple image upload requested");

            $images = $this->imageValidationService->validateUploadRequest();
            $this->imageValidationService->validateImageFiles($images);
            $uploadedImages = $this->imageUploadProcessor->processImageUploads($images);

            $this->returnArrayData["images"] = $uploadedImages;
            $this->returnArrayData["message"] = __('%1 image(s) have been successfully uploaded', count($uploadedImages));
            $this->returnArrayData["success"] = true;
            $this->returnArrayData["count"] = count($uploadedImages);
            $this->returnArrayData["admin_upload"] = true;

            return $this->getJsonResponse($this->returnArrayData);

        } catch (\Exception $e) {
            $this->mpHelper->logDataInLogger($e->getMessage());
            $this->returnArrayData["message"] = $e->getMessage();
            $this->returnArrayData["success"] = false;
            return $this->getJsonResponse($this->returnArrayData);
        }
    }

    /**
     * Get JSON response
     *
     * @param array $data
     * @return string
     */
    private function getJsonResponse(array $data): string
    {
        $resultJson = $this->jsonFactory->create();
        $resultJson->setData($data);
        return $resultJson->getJson();
    }
}
