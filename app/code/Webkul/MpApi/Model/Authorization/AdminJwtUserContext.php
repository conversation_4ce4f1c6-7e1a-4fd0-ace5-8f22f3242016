<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */

namespace Webkul\MpApi\Model\Authorization;

use Magento\Authorization\Model\UserContextInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\User\Model\UserFactory;
use Magento\User\Model\ResourceModel\User as UserResource;
use Psr\Log\LoggerInterface;

/**
 * Admin JWT User Context for REST API authentication
 */
class AdminJwtUserContext implements UserContextInterface
{
    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var UserFactory
     */
    private $userFactory;

    /**
     * @var UserResource
     */
    private $userResource;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var int|null
     */
    private $userId;

    /**
     * @var int|null
     */
    private $userType;

    /**
     * @var bool
     */
    private $isProcessed = false;

    /**
     * Constructor
     *
     * @param RequestInterface $request
     * @param UserFactory $userFactory
     * @param UserResource $userResource
     * @param LoggerInterface $logger
     */
    public function __construct(
        RequestInterface $request,
        UserFactory $userFactory,
        UserResource $userResource,
        LoggerInterface $logger
    ) {
        $this->request = $request;
        $this->userFactory = $userFactory;
        $this->userResource = $userResource;
        $this->logger = $logger;
    }

    /**
     * Get user ID
     *
     * @return int|null
     */
    public function getUserId()
    {
        $this->processRequest();
        return $this->userId;
    }

    /**
     * Get user type
     *
     * @return int|null
     */
    public function getUserType()
    {
        $this->processRequest();
        return $this->userType;
    }

    /**
     * Process the request to extract user information from JWT token
     *
     * @return void
     */
    private function processRequest()
    {
        if ($this->isProcessed) {
            return;
        }

        $this->isProcessed = true;

        try {
            $authorizationHeader = $this->request->getHeader('Authorization');
            if (!$authorizationHeader) {
                return;
            }

            // Extract Bearer token
            if (!preg_match('/Bearer\s+(.*)$/i', $authorizationHeader, $matches)) {
                return;
            }

            $token = $matches[1];
            $tokenData = $this->decodeJwtToken($token);

            if (!$tokenData) {
                return;
            }

            // Check if this is an admin token
            if (isset($tokenData['utypid']) && $tokenData['utypid'] == 1) {
                // Admin user type
                $adminUserId = $tokenData['uid'] ?? null;
                
                if ($adminUserId && $this->validateAdminUser($adminUserId)) {
                    $this->userId = (int)$adminUserId;
                    $this->userType = UserContextInterface::USER_TYPE_ADMIN;
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('AdminJwtUserContext error: ' . $e->getMessage());
        }
    }

    /**
     * Decode JWT token
     *
     * @param string $token
     * @return array|null
     */
    private function decodeJwtToken($token)
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            // Decode the payload (second part)
            $payload = $parts[1];
            
            // Add padding if needed
            $payload .= str_repeat('=', (4 - strlen($payload) % 4) % 4);
            
            $decoded = base64_decode($payload);
            if (!$decoded) {
                return null;
            }

            $data = json_decode($decoded, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }

            // Check token expiration
            if (isset($data['exp']) && $data['exp'] < time()) {
                $this->logger->info('JWT token expired');
                return null;
            }

            return $data;

        } catch (\Exception $e) {
            $this->logger->error('JWT decode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate admin user exists and is active
     *
     * @param int $userId
     * @return bool
     */
    private function validateAdminUser($userId)
    {
        try {
            $user = $this->userFactory->create();
            $this->userResource->load($user, $userId);

            if (!$user->getId()) {
                return false;
            }

            // Check if user is active
            if (!$user->getIsActive()) {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Admin user validation error: ' . $e->getMessage());
            return false;
        }
    }
}
