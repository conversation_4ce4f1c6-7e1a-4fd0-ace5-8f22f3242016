<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */

namespace Webkul\MpApi\Model\Authorization;

use Magento\Authorization\Model\UserContextInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\User\Model\UserFactory;
use Magento\User\Model\ResourceModel\User as UserResource;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResource;
use Psr\Log\LoggerInterface;

/**
 * Custom JWT User Context for REST API authentication
 * Handles both admin and customer JWT tokens
 */
class AdminJwtUserContext implements UserContextInterface
{
    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var UserFactory
     */
    private $userFactory;

    /**
     * @var UserResource
     */
    private $userResource;

    /**
     * @var CustomerFactory
     */
    private $customerFactory;

    /**
     * @var CustomerResource
     */
    private $customerResource;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var int|null
     */
    private $userId;

    /**
     * @var int|null
     */
    private $userType;

    /**
     * @var bool
     */
    private $isProcessed = false;

    /**
     * Constructor
     *
     * @param RequestInterface $request
     * @param UserFactory $userFactory
     * @param UserResource $userResource
     * @param CustomerFactory $customerFactory
     * @param CustomerResource $customerResource
     * @param LoggerInterface $logger
     */
    public function __construct(
        RequestInterface $request,
        UserFactory $userFactory,
        UserResource $userResource,
        CustomerFactory $customerFactory,
        CustomerResource $customerResource,
        LoggerInterface $logger
    ) {
        $this->request = $request;
        $this->userFactory = $userFactory;
        $this->userResource = $userResource;
        $this->customerFactory = $customerFactory;
        $this->customerResource = $customerResource;
        $this->logger = $logger;
    }

    /**
     * Get user ID
     *
     * @return int|null
     */
    public function getUserId()
    {
        $this->processRequest();
        return $this->userId;
    }

    /**
     * Get user type
     *
     * @return int|null
     */
    public function getUserType()
    {
        $this->processRequest();
        return $this->userType;
    }

    /**
     * Process the request to extract user information from JWT token
     *
     * @return void
     */
    private function processRequest()
    {
        if ($this->isProcessed) {
            return;
        }

        $this->isProcessed = true;

        try {
            $this->logger->info("JWT User Context: Processing request");

            $authorizationHeader = $this->request->getHeader('Authorization');
            if (!$authorizationHeader) {
                $this->logger->info("JWT User Context: No Authorization header found");
                return;
            }

            // Extract Bearer token
            if (!preg_match('/Bearer\s+(.*)$/i', $authorizationHeader, $matches)) {
                $this->logger->info("JWT User Context: No Bearer token found in Authorization header");
                return;
            }

            $token = $matches[1];
            $this->logger->info("JWT User Context: Found Bearer token: " . substr($token, 0, 20) . "...");

            $tokenData = $this->decodeJwtToken($token);

            if (!$tokenData) {
                $this->logger->info("JWT User Context: Failed to decode JWT token");
                return;
            }

            $this->logger->info("JWT User Context: Token decoded successfully", $tokenData);

            // Handle different user types based on utypid
            $userId = $tokenData['uid'] ?? null;
            $userTypeId = $tokenData['utypid'] ?? null;

            if (!$userId || !$userTypeId) {
                return;
            }

            if ($userTypeId == 1) {
                // Admin user type - treat as customer for webapi compatibility
                if ($this->validateAdminUser($userId)) {
                    $this->userId = (int)$userId;
                    $this->userType = UserContextInterface::USER_TYPE_CUSTOMER; // Use customer type for %customer_id% compatibility
                    $this->logger->info("Admin user {$userId} authenticated via JWT token");
                }
            } elseif ($userTypeId == 3) {
                // Customer user type
                if ($this->validateCustomerUser($userId)) {
                    $this->userId = (int)$userId;
                    $this->userType = UserContextInterface::USER_TYPE_CUSTOMER;
                    $this->logger->info("Customer user {$userId} authenticated via JWT token");
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('AdminJwtUserContext error: ' . $e->getMessage());
        }
    }

    /**
     * Decode JWT token
     *
     * @param string $token
     * @return array|null
     */
    private function decodeJwtToken($token)
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            // Decode the payload (second part)
            $payload = $parts[1];
            
            // Add padding if needed
            $payload .= str_repeat('=', (4 - strlen($payload) % 4) % 4);
            
            $decoded = base64_decode($payload);
            if (!$decoded) {
                return null;
            }

            $data = json_decode($decoded, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }

            // Check token expiration
            if (isset($data['exp']) && $data['exp'] < time()) {
                $this->logger->info('JWT token expired');
                return null;
            }

            return $data;

        } catch (\Exception $e) {
            $this->logger->error('JWT decode error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate admin user exists and is active
     *
     * @param int $userId
     * @return bool
     */
    private function validateAdminUser($userId)
    {
        try {
            $user = $this->userFactory->create();
            $this->userResource->load($user, $userId);

            if (!$user->getId()) {
                return false;
            }

            // Check if user is active
            if (!$user->getIsActive()) {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Admin user validation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate customer user exists and is active
     *
     * @param int $userId
     * @return bool
     */
    private function validateCustomerUser($userId)
    {
        try {
            $customer = $this->customerFactory->create();
            $this->customerResource->load($customer, $userId);

            if (!$customer->getId()) {
                return false;
            }

            // Check if customer is active (not disabled)
            if ($customer->getIsActive() === '0') {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Customer user validation error: ' . $e->getMessage());
            return false;
        }
    }
}
