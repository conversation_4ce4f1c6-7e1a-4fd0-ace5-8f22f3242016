<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */

namespace Webkul\MpApi\Api;

/**
 * Admin Token Management Interface
 */
interface AdminTokenManagementInterface
{
    /**
     * Generate admin JWT token
     *
     * @param string $username
     * @param string $password
     * @return string
     * @throws \Magento\Framework\Exception\AuthenticationException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function createAdminAccessToken($username, $password);

    /**
     * Validate admin JWT token
     *
     * @param string $token
     * @return array|null
     */
    public function validateAdminToken($token);
}
